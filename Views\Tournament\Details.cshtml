@model WebQuanLyGiaiDau_NhomTD.Models.Tournament
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel

@{
    ViewData["Title"] = "Chi Tiết Giải Đấu";
    var teams = ViewBag.Teams as List<WebQuanLyGiaiDau_NhomTD.Models.Team>;
    var matches = ViewBag.Matches as List<WebQuanLyGiaiDau_NhomTD.Models.Match>;
    var teamRankings = ViewBag.TeamRankings as List<dynamic>;
    var matchStatus = ViewBag.MatchStatus as Dictionary<int, string>;
}

<style>
    .match-row:hover {
        background-color: #f0f8ff !important;
        transition: background-color 0.3s;
    }
</style>

<div class="container mt-4">
    <h1 class="text-center text-primary mb-4">
        <i class="bi bi-info-circle"></i> Chi Tiết Giải Đấu
    </h1>

    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card shadow mb-4">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">@Model.Name</h4>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        @if (!string.IsNullOrEmpty(Model.ImageUrl))
                        {
                            <div class="col-md-4 text-center mb-3">
                                <img src="@Model.ImageUrl" alt="Ảnh Giải Đấu" class="img-fluid rounded border" style="max-height: 200px;" />
                            </div>
                            <div class="col-md-8">
                                <dl class="row">
                                    <dt class="col-sm-4 fw-bold">Mô Tả</dt>
                                    <dd class="col-sm-8"><span class="tournament-description">@Html.DisplayFor(model => model.Description)</span></dd>

                                    <dt class="col-sm-4 fw-bold">Địa Điểm</dt>
                                    <dd class="col-sm-8">
                                        @if (!string.IsNullOrEmpty(Model.Location))
                                        {
                                            @Model.Location
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa xác định</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-4 fw-bold">Ngày Bắt Đầu</dt>
                                    <dd class="col-sm-8">@Model.StartDate.ToString("dd/MM/yyyy")</dd>

                                    <dt class="col-sm-4 fw-bold">Ngày Kết Thúc</dt>
                                    <dd class="col-sm-8">@Model.EndDate.ToString("dd/MM/yyyy")</dd>

                                    <dt class="col-sm-4 fw-bold">Môn Thể Thao</dt>
                                    <dd class="col-sm-8">@(Model.Sports?.Name ?? "Không xác định")</dd>

                                    <dt class="col-sm-4 fw-bold">Thể Thức Thi Đấu</dt>
                                    <dd class="col-sm-8">
                                        @if (Model.TournamentFormat != null)
                                        {
                                            <span class="badge bg-info">@Model.TournamentFormat.Name</span>
                                            <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#formatDetailsModal">
                                                <i class="bi bi-info-circle"></i> Chi tiết
                                            </button>

                                            <!-- Modal hiển thị chi tiết thể thức thi đấu -->
                                            <div class="modal fade" id="formatDetailsModal" tabindex="-1" aria-labelledby="formatDetailsModalLabel" aria-hidden="true">
                                                <div class="modal-dialog modal-lg">
                                                    <div class="modal-content">
                                                        <div class="modal-header bg-info text-white">
                                                            <h5 class="modal-title" id="formatDetailsModalLabel">
                                                                <i class="bi bi-trophy"></i> Thể Thức Thi Đấu: @Model.TournamentFormat.Name
                                                            </h5>
                                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                        </div>
                                                        <div class="modal-body">
                                                            <div class="row">
                                                                <div class="col-12 mb-3">
                                                                    <h6 class="fw-bold text-primary">
                                                                        <i class="bi bi-info-circle"></i> Mô Tả
                                                                    </h6>
                                                                    <p>@Model.TournamentFormat.Description</p>
                                                                </div>

                                                                <div class="col-md-6 mb-3">
                                                                    <h6 class="fw-bold text-primary">
                                                                        <i class="bi bi-calculator"></i> Cách Tính Điểm
                                                                    </h6>
                                                                    <p>@Model.TournamentFormat.ScoringRules</p>
                                                                </div>

                                                                <div class="col-md-6 mb-3">
                                                                    <h6 class="fw-bold text-primary">
                                                                        <i class="bi bi-award"></i> Cách Xác Định Đội Chiến Thắng
                                                                    </h6>
                                                                    <p>@Model.TournamentFormat.WinnerDetermination</p>
                                                                </div>

                                                                <div class="col-12">
                                                                    <div class="alert alert-info">
                                                                        <i class="bi bi-lightbulb"></i> <strong>Lưu ý:</strong>
                                                                        @if (Model.TournamentFormat.Name.Contains("Vòng tròn"))
                                                                        {
                                                                            <span>Trong thể thức vòng tròn, mỗi đội sẽ thi đấu với tất cả các đội còn lại.</span>
                                                                        }
                                                                        else if (Model.TournamentFormat.Name.Contains("Loại trực tiếp"))
                                                                        {
                                                                            <span>Trong thể thức loại trực tiếp, đội thua sẽ bị loại khỏi giải đấu.</span>
                                                                        }
                                                                        else if (Model.TournamentFormat.Name.Contains("Vòng bảng"))
                                                                        {
                                                                            <span>Trong thể thức vòng bảng, các đội được chia thành các bảng đấu và thi đấu vòng tròn trong bảng.</span>
                                                                        }
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa xác định</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-4 fw-bold">Số Đội Tối Đa</dt>
                                    <dd class="col-sm-8">@(Model.MaxTeams ?? 8) đội</dd>

                                    <dt class="col-sm-4 fw-bold">Trạng Thái</dt>
                                    <dd class="col-sm-8">
                                        @{
                                            string statusClass = Model.CalculatedStatus switch
                                            {
                                                "Mở đăng ký" => "bg-success",
                                                "Chưa mở đăng ký" => "bg-secondary",
                                                "Kết thúc đăng ký" => "bg-warning",
                                                "Giải đấu đang diễn ra" => "bg-danger",
                                                "Giải đấu đã kết thúc" => "bg-dark",
                                                _ => "bg-info"
                                            };
                                        }
                                        <span class="badge @statusClass">@Model.CalculatedStatus</span>
                                    </dd>
                                </dl>
                            </div>
                        }
                        else
                        {
                            <div class="col-md-12">
                                <dl class="row">
                                    <dt class="col-sm-3 fw-bold">Mô Tả</dt>
                                    <dd class="col-sm-9"><span class="tournament-description">@Html.DisplayFor(model => model.Description)</span></dd>

                                    <dt class="col-sm-3 fw-bold">Địa Điểm</dt>
                                    <dd class="col-sm-9">
                                        @if (!string.IsNullOrEmpty(Model.Location))
                                        {
                                            @Model.Location
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa xác định</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-3 fw-bold">Ngày Bắt Đầu</dt>
                                    <dd class="col-sm-9">@Model.StartDate.ToString("dd/MM/yyyy")</dd>

                                    <dt class="col-sm-3 fw-bold">Ngày Kết Thúc</dt>
                                    <dd class="col-sm-9">@Model.EndDate.ToString("dd/MM/yyyy")</dd>

                                    <dt class="col-sm-3 fw-bold">Môn Thể Thao</dt>
                                    <dd class="col-sm-9">@(Model.Sports?.Name ?? "Không xác định")</dd>

                                    <dt class="col-sm-3 fw-bold">Thể Thức Thi Đấu</dt>
                                    <dd class="col-sm-9">
                                        @if (Model.TournamentFormat != null)
                                        {
                                            <span class="badge bg-info">@Model.TournamentFormat.Name</span>
                                            <button type="button" class="btn btn-sm btn-outline-info ms-2"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#formatDetailsModal">
                                                <i class="bi bi-info-circle"></i> Chi tiết
                                            </button>
                                        }
                                        else
                                        {
                                            <span class="text-muted">Chưa xác định</span>
                                        }
                                    </dd>

                                    <dt class="col-sm-3 fw-bold">Số Đội Tối Đa</dt>
                                    <dd class="col-sm-9">@(Model.MaxTeams ?? 8) đội</dd>

                                    <dt class="col-sm-3 fw-bold">Trạng Thái</dt>
                                    <dd class="col-sm-9">
                                        @{
                                            string statusClass = Model.CalculatedStatus switch
                                            {
                                                "Mở đăng ký" => "bg-success",
                                                "Chưa mở đăng ký" => "bg-secondary",
                                                "Kết thúc đăng ký" => "bg-warning",
                                                "Giải đấu đang diễn ra" => "bg-danger",
                                                "Giải đấu đã kết thúc" => "bg-dark",
                                                _ => "bg-info"
                                            };
                                        }
                                        <span class="badge @statusClass">@Model.CalculatedStatus</span>
                                    </dd>
                                </dl>
                            </div>
                        }
                    </div>

                    <!-- Hiển thị các đội tham gia -->
                    <div class="mt-4">
                        <h5 class="text-primary mb-3">
                            <i class="bi bi-people-fill"></i> Các Đội Tham Gia
                        </h5>

                        @if (teams != null && teams.Any())
                        {
                            <div class="row row-cols-2 row-cols-md-4 g-4">
                                @foreach (var team in teams)
                                {
                                    <div class="col">
                                        <div class="card h-100 shadow-sm text-center">
                                            <a href="@Url.Action("Details", "Teams", new { id = team.TeamId })" class="text-decoration-none">
                                                @if (!string.IsNullOrEmpty(team.LogoUrl))
                                                {
                                                    <img src="@team.LogoUrl" alt="Logo @team.Name" class="card-img-top p-3" style="height: 120px; object-fit: contain;" />
                                                }
                                                else
                                                {
                                                    <div class="bg-light d-flex align-items-center justify-content-center p-3" style="height: 120px;">
                                                        <i class="bi bi-shield-fill text-secondary" style="font-size: 3rem;"></i>
                                                    </div>
                                                }
                                                <div class="card-body">
                                                    <h6 class="card-title text-primary">@team.Name</h6>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                }
                            </div>
                        }
                        else
                        {
                            <div class="alert alert-info">
                                <i class="bi bi-info-circle"></i> Chưa có đội bóng nào tham gia giải đấu này.
                            </div>
                        }
                    </div>

                    <!-- Không cần khai báo lại biến teamRankings vì đã khai báo ở đầu trang -->

                    <!-- Bảng xếp hạng và Lịch thi đấu -->
                    <div class="mt-4">
                        <!-- Tabs cho Bảng xếp hạng và Lịch thi đấu -->
                        <ul class="nav nav-pills mb-3" id="tournamentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="standings-tab" data-bs-toggle="pill" data-bs-target="#standings-content" type="button" role="tab" aria-controls="standings-content" aria-selected="true">
                                    <i class="bi bi-bar-chart-fill"></i> Bảng Xếp Hạng
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="schedule-tab" data-bs-toggle="pill" data-bs-target="#schedule-content" type="button" role="tab" aria-controls="schedule-content" aria-selected="false">
                                    <i class="bi bi-calendar-event"></i> Lịch Thi Đấu
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="player-stats-tab" data-bs-toggle="pill" data-bs-target="#player-stats-content" type="button" role="tab" aria-controls="player-stats-content" aria-selected="false">
                                    <i class="bi bi-person-badge"></i> Thống Kê Cầu Thủ
                                </button>
                            </li>
                        </ul>

                        <!-- Tab content -->
                        <div class="tab-content" id="tournamentTabsContent">
                            <!-- Bảng xếp hạng content -->
                            <div class="tab-pane fade show active" id="standings-content" role="tabpanel" aria-labelledby="standings-tab">
                                @if (teams != null && teams.Any() && teamRankings != null && teamRankings.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover standings-table">
                                            <thead class="table-dark">
                                                <tr class="text-center">
                                                    <th>Hạng</th>
                                                    <th>Đội</th>
                                                    <th>Trận</th>
                                                    <th>Thắng</th>
                                                    <th>Hòa</th>
                                                    <th>Thua</th>
                                                    <th>
                                                        Ghi
                                                        <button class="btn btn-sm btn-outline-light ms-1 sort-standings" data-sort="scored" title="Sắp xếp theo điểm ghi">
                                                            <i class="bi bi-sort-down"></i>
                                                        </button>
                                                    </th>
                                                    <th>Thủng</th>
                                                    <th>
                                                        Hiệu số
                                                        <button class="btn btn-sm btn-outline-light ms-1 sort-standings" data-sort="diff" title="Sắp xếp theo hiệu số">
                                                            <i class="bi bi-sort-down"></i>
                                                        </button>
                                                    </th>
                                                    <th>
                                                        Điểm
                                                        <button class="btn btn-sm btn-outline-light ms-1 sort-standings" data-sort="points" title="Sắp xếp theo điểm số">
                                                            <i class="bi bi-sort-down"></i>
                                                        </button>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for (int i = 0; i < teamRankings.Count; i++)
                                                {
                                                    var ranking = teamRankings[i];
                                                    var team = ranking.Team;

                                                    <tr class="@(i < 3 ? "table-success" : "")">
                                                        <td class="text-center fw-bold">@(i + 1)</td>
                                                        <td>
                                                            @if (team != null)
                                                            {
                                                                if (!string.IsNullOrEmpty(team.LogoUrl))
                                                                {
                                                                    <img src="@team.LogoUrl" alt="Logo @team.Name" class="me-2" style="height: 24px; width: auto;" />
                                                                }
                                                                @team.Name
                                                            }
                                                            else
                                                            {
                                                                <span class="text-muted">Không có đội</span>
                                                            }
                                                        </td>
                                                        <td class="text-center">@ranking.Played</td>
                                                        <td class="text-center">@ranking.Won</td>
                                                        <td class="text-center">@ranking.Drawn</td>
                                                        <td class="text-center">@ranking.Lost</td>
                                                        <td class="text-center">@ranking.PointsScored</td>
                                                        <td class="text-center">@ranking.PointsConceded</td>
                                                        <td class="text-center">@(ranking.PointDiff > 0 ? "+" + ranking.PointDiff : ranking.PointDiff.ToString())</td>
                                                        <td class="text-center fw-bold">@ranking.Points</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> Chưa có đội bóng nào tham gia giải đấu này.
                                    </div>
                                }
                            </div>

                            <!-- Lịch thi đấu content -->
                            <div class="tab-pane fade" id="schedule-content" role="tabpanel" aria-labelledby="schedule-tab">
                                @if (User.IsInRole(SD.Role_Admin) || User.IsInRole(SD.Role_User))
                                {
                                    <div class="mb-3">
                                        <a asp-action="GenerateSchedule" asp-route-id="@Model.Id" class="btn btn-primary">
                                            <i class="bi bi-calendar-plus"></i> Tạo lịch thi đấu tự động
                                        </a>
                                    </div>
                                }

                                @if (matches != null && matches.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover">
                                            <thead class="table-primary">
                                                <tr>
                                                    <th>Đội A</th>
                                                    <th class="text-center">Tỉ Số</th>
                                                    <th>Đội B</th>
                                                    <th class="text-center">Thời Gian Thi Đấu</th>
                                                    <th class="text-center">Trạng Thái</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach (var match in matches)
                                                {
                                                    // Lấy trạng thái trận đấu từ dictionary
                                                    string status = (matchStatus != null && matchStatus.ContainsKey(match.Id)) ? matchStatus[match.Id] : "Upcoming";

                                                    string statusText = status == "Completed" ? "Đã kết thúc" :
                                                                       (status == "InProgress" ? "Đang diễn ra" : "Sắp diễn ra");
                                                    string statusClass = status == "Completed" ? "bg-success" :
                                                                       (status == "InProgress" ? "bg-danger" : "bg-primary");

                                                    <tr class="match-row" style="cursor: pointer;" onclick="location.href='@Url.Action("Details", "Match", new { id = match.Id })'">
                                                        <td class="fw-bold">
                                                            @if (!string.IsNullOrEmpty(match.TeamA))
                                                            {
                                                                @match.TeamA
                                                            }
                                                            else
                                                            {
                                                                <span class="text-muted">Chưa xác định</span>
                                                            }
                                                        </td>
                                                        <td class="text-center">
                                                            @if (status == "Completed")
                                                            {
                                                                @if (match.ScoreTeamA.HasValue && match.ScoreTeamB.HasValue)
                                                                {
                                                                    <span class="badge bg-secondary">@match.ScoreTeamA - @match.ScoreTeamB</span>
                                                                }
                                                                else
                                                                {
                                                                    <span class="badge bg-secondary">0 - 0</span>
                                                                }
                                                            }
                                                            else if (status == "InProgress")
                                                            {
                                                                @if (match.ScoreTeamA.HasValue && match.ScoreTeamB.HasValue)
                                                                {
                                                                    <span class="badge bg-danger">@match.ScoreTeamA - @match.ScoreTeamB</span>
                                                                }
                                                                else
                                                                {
                                                                    <span class="badge bg-danger">0 - 0</span>
                                                                }
                                                            }
                                                            else
                                                            {
                                                                <span class="badge bg-secondary">VS</span>
                                                            }
                                                        </td>
                                                        <td class="fw-bold">
                                                            @if (!string.IsNullOrEmpty(match.TeamB))
                                                            {
                                                                @match.TeamB
                                                            }
                                                            else
                                                            {
                                                                <span class="text-muted">Chưa xác định</span>
                                                            }
                                                        </td>
                                                        <td class="text-center">
                                                            @if (match.MatchDate != default(DateTime))
                                                            {
                                                                <div>@match.MatchDate.ToString("dd/MM/yyyy")</div>
                                                                @if (Model.Name.Contains("5v5") || Model.Name.Contains("5 vs 5"))
                                                                {
                                                                    <div><small class="text-muted">15:00 - 16:08</small></div>
                                                                    <div><small class="text-muted fst-italic">(4 hiệp x 12 phút)</small></div>
                                                                }
                                                                else
                                                                {
                                                                    <div><small class="text-muted">15:00 - 15:15</small></div>
                                                                    <div><small class="text-muted fst-italic">(Hoặc đến khi đạt 21 điểm)</small></div>
                                                                }
                                                            }
                                                            else
                                                            {
                                                                <span class="text-muted">Chưa xác định</span>
                                                            }
                                                        </td>
                                                        <td class="text-center">
                                                            <span class="badge @statusClass">@statusText</span>
                                                            <div class="mt-1">
                                                                <small class="text-primary"><i class="bi bi-info-circle"></i> Nhấn để xem chi tiết</small>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> Chưa có trận đấu nào được lên lịch cho giải đấu này.

                                        @if (User.IsInRole(SD.Role_Admin) || User.IsInRole(SD.Role_User))
                                        {
                                            <div class="mt-3">
                                                <a asp-action="GenerateSchedule" asp-route-id="@Model.Id" class="btn btn-primary">
                                                    <i class="bi bi-calendar-plus"></i> Tạo lịch thi đấu tự động
                                                </a>
                                            </div>
                                        }
                                    </div>
                                }
                            </div>

                            <!-- Thống kê cầu thủ content -->
                            <div class="tab-pane fade" id="player-stats-content" role="tabpanel" aria-labelledby="player-stats-tab">
                                @{
                                    var playerStats = ViewBag.PlayerStats as List<dynamic>;
                                }

                                @if (playerStats != null && playerStats.Any())
                                {
                                    <div class="table-responsive">
                                        <table class="table table-striped table-hover" id="playerStatsTable">
                                            <thead class="table-primary">
                                                <tr class="text-center">
                                                    <th>Hạng</th>
                                                    <th>Cầu Thủ</th>
                                                    <th>Đội</th>
                                                    <th class="text-center">Trận</th>
                                                    <th class="text-center">
                                                        Điểm
                                                        <button class="btn btn-sm btn-outline-primary ms-1" id="sortByPlayerPoints" title="Sắp xếp theo điểm số">
                                                            <i class="bi bi-sort-down"></i>
                                                        </button>
                                                    </th>
                                                    <th class="text-center">TB Điểm</th>
                                                    <th class="text-center">
                                                        Kiến Tạo
                                                        <button class="btn btn-sm btn-outline-primary ms-1" id="sortByPlayerAssists" title="Sắp xếp theo kiến tạo">
                                                            <i class="bi bi-sort-down"></i>
                                                        </button>
                                                    </th>
                                                    <th class="text-center">
                                                        Bắt Bóng
                                                        <button class="btn btn-sm btn-outline-primary ms-1" id="sortByPlayerRebounds" title="Sắp xếp theo bắt bóng">
                                                            <i class="bi bi-sort-down"></i>
                                                        </button>
                                                    </th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @for (int i = 0; i < playerStats.Count; i++)
                                                {
                                                    var player = playerStats[i];
                                                    <tr class="@(i < 3 ? "table-warning" : "")">
                                                        <td class="text-center fw-bold">@(i + 1)</td>
                                                        <td>
                                                            <a href="@Url.Action("FindPlayerByName", "Tournament", new { playerName = player.PlayerName, tournamentId = Model.Id })" class="text-decoration-none">
                                                                @player.PlayerName
                                                                <small class="d-block text-muted"><i class="bi bi-info-circle"></i> Nhấn để xem chi tiết</small>
                                                            </a>
                                                        </td>
                                                        <td>@player.Team</td>
                                                        <td class="text-center">@player.GamesPlayed</td>
                                                        <td class="text-center fw-bold">@player.TotalPoints</td>
                                                        <td class="text-center">@player.PointsPerGame</td>
                                                        <td class="text-center">@player.TotalAssists</td>
                                                        <td class="text-center">@player.TotalRebounds</td>
                                                    </tr>
                                                }
                                            </tbody>
                                        </table>
                                    </div>
                                }
                                else
                                {
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i> Chưa có thống kê cầu thủ nào cho giải đấu này.
                                    </div>
                                }
                            </div>
                        </div>
                    </div>



                    <div class="d-flex justify-content-between mt-4">
                        <div>
                            <a asp-action="Index" class="btn btn-secondary me-2">
                                <i class="bi bi-list"></i> Danh Sách Giải Đấu
                            </a>
                            @if (Model.SportsId > 0)
                            {
                                <a asp-controller="Sports" asp-action="List" asp-route-sportsId="@Model.SportsId" class="btn btn-info">
                                    <i class="bi bi-arrow-left"></i> Quay Lại Môn Thể Thao
                                </a>
                            }
                        </div>
                        <div>
                            @if (User.IsInRole(SD.Role_User) && Model.CalculatedStatus == "Mở đăng ký")
                            {
                                <a asp-action="Register" asp-route-id="@Model.Id" class="btn btn-success me-2">
                                    <i class="bi bi-person-plus"></i> Đăng Ký Tham Gia
                                </a>
                                <a asp-action="RegisterTeam" asp-route-id="@Model.Id" class="btn btn-primary me-2">
                                    <i class="bi bi-people-fill"></i> Đăng Ký Đội
                                </a>
                            }
                            @if (User.IsInRole(SD.Role_Admin))
                            {
                                <a asp-action="Edit" asp-route-id="@Model.Id" class="btn btn-warning me-2">
                                    <i class="bi bi-pencil"></i> Chỉnh Sửa
                                </a>
                                <a asp-action="Delete" asp-route-id="@Model.Id" class="btn btn-danger">
                                    <i class="bi bi-trash"></i> Xóa
                                </a>
                            }
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        $(document).ready(function () {
            // Hiển thị tab được chọn từ URL hash
            var hash = window.location.hash;
            if (hash) {
                $('#tournamentTabs a[href="' + hash + '"]').tab('show');
            }

            // Cập nhật URL hash khi chuyển tab
            $('#tournamentTabs a').on('shown.bs.tab', function (e) {
                window.location.hash = e.target.hash;
            });

            // Kích hoạt popovers cho thông tin thể thức thi đấu
            var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'))
            var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
                return new bootstrap.Popover(popoverTriggerEl, {
                    html: true
                });
            });

            // Khởi tạo modal
            var formatDetailsModal = document.getElementById('formatDetailsModal');
            if (formatDetailsModal) {
                var modal = new bootstrap.Modal(formatDetailsModal);

                // Thêm sự kiện khi modal hiển thị
                formatDetailsModal.addEventListener('shown.bs.modal', function () {
                    console.log('Modal hiển thị thành công');
                });
            }

            // Thêm chức năng sắp xếp bảng xếp hạng
            $(".sort-standings").click(function() {
                var sortBy = $(this).data("sort");
                var rows = $(".standings-table tbody tr").get();

                rows.sort(function(a, b) {
                    var keyA, keyB;

                    if (sortBy === "points") {
                        keyA = parseInt($(a).find("td:nth-child(10)").text());
                        keyB = parseInt($(b).find("td:nth-child(10)").text());
                    } else if (sortBy === "scored") {
                        keyA = parseInt($(a).find("td:nth-child(7)").text());
                        keyB = parseInt($(b).find("td:nth-child(7)").text());
                    } else if (sortBy === "diff") {
                        keyA = parseInt($(a).find("td:nth-child(9)").text().replace("+", ""));
                        keyB = parseInt($(b).find("td:nth-child(9)").text().replace("+", ""));
                    }

                    return keyB - keyA;
                });

                $.each(rows, function(index, row) {
                    $(".standings-table tbody").append(row);
                    $(row).find("td:first-child").text(index + 1);
                });

                return false;
            });

            // Sắp xếp bảng thống kê cầu thủ theo điểm số
            $("#sortByPlayerPoints").click(function() {
                sortPlayerStats(5); // Cột điểm số
                return false;
            });

            // Sắp xếp bảng thống kê cầu thủ theo kiến tạo
            $("#sortByPlayerAssists").click(function() {
                sortPlayerStats(7); // Cột kiến tạo
                return false;
            });

            // Sắp xếp bảng thống kê cầu thủ theo bắt bóng
            $("#sortByPlayerRebounds").click(function() {
                sortPlayerStats(8); // Cột bắt bóng
                return false;
            });

            // Hàm sắp xếp bảng thống kê cầu thủ
            function sortPlayerStats(columnIndex) {
                var rows = $("#playerStatsTable tbody tr").get();

                rows.sort(function(a, b) {
                    var keyA = parseInt($(a).find("td:nth-child(" + columnIndex + ")").text());
                    var keyB = parseInt($(b).find("td:nth-child(" + columnIndex + ")").text());
                    return keyB - keyA;
                });

                $.each(rows, function(index, row) {
                    $("#playerStatsTable tbody").append(row);
                    $(row).find("td:first-child").text(index + 1);

                    // Cập nhật lớp CSS cho 3 cầu thủ đứng đầu
                    if (index < 3) {
                        $(row).addClass("table-warning");
                    } else {
                        $(row).removeClass("table-warning");
                    }
                });
            }
        });
    </script>
}
