@model WebQuanLyGiaiDau_NhomTD.Models.Sports
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel

@{
    ViewData["Title"] = "Thêm Môn Thể <PERSON>hao <PERSON>";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-plus-circle"></i> Thêm Môn Thể Thao Mới
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Create" method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>

                        <div class="mb-3">
                            <label asp-for="Name" class="form-label">Tên <PERSON><PERSON>h<PERSON>hao</label>
                            <input asp-for="Name" class="form-control" required />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label for="imageUrl" class="form-label">Hình Ảnh</label>
                            <input type="file" name="imageUrl" id="imageUrl" class="form-control" accept="image/*" />
                            <small class="form-text text-muted">Chọn hình ảnh đại diện cho môn thể thao (không bắt buộc)</small>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Quay Lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Lưu
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
