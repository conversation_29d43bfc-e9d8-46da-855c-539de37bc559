@model WebQuanLyGiaiDau_NhomTD.Models.Match

@{
    ViewData["Title"] = "Chi Tiết Trận Đấu";
    var matchSets = ViewBag.MatchSets as List<dynamic>;
    var matchStatus = ViewBag.MatchStatus as string;
    var matchEndTime = ViewBag.MatchEndTime as DateTime?;
    var playerScorings = ViewBag.PlayerScorings as List<WebQuanLyGiaiDau_NhomTD.Models.PlayerScoring>;
}

<h1 class="text-center text-info mt-4">📋 Chi Tiết Trận Đấu</h1>

<div class="container bg-light shadow rounded p-4 mt-4 mb-4">
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thông Tin Trận Đấu</h5>
                </div>
                <div class="card-body">
                    <div class="row align-items-center mb-4">
                        <div class="col-md-4 col-6 text-center">
                            <h4 class="team-name">@Model.TeamA</h4>
                        </div>
                        <div class="col-md-4 col-12 order-md-1 order-last text-center my-3 my-md-0">
                            <div class="score-display p-3 bg-light rounded shadow-sm">
                                @if (matchStatus == "Completed" || matchStatus == "InProgress")
                                {
                                    <h2 class="mb-0 fw-bold">@(Model.ScoreTeamA ?? 0) - @(Model.ScoreTeamB ?? 0)</h2>
                                    <div class="badge @(matchStatus == "Completed" ? "bg-success" : "bg-danger") mt-2">
                                        @(matchStatus == "Completed" ? "Đã kết thúc" : "Đang diễn ra")
                                    </div>
                                }
                                else
                                {
                                    <h2 class="mb-0 fw-bold">VS</h2>
                                    <div class="badge bg-primary mt-2">Sắp diễn ra</div>
                                }
                            </div>
                        </div>
                        <div class="col-md-4 col-6 text-center">
                            <h4 class="team-name">@Model.TeamB</h4>
                        </div>
                    </div>

                    <dl class="row">
                        <dt class="col-sm-3 fw-bold">Ngày Thi Đấu:</dt>
                        <dd class="col-sm-9">
                            @if (Model.MatchDate != default(DateTime))
                            {
                                @Model.MatchDate.ToString("dd/MM/yyyy")
                            }
                            else
                            {
                                <span class="text-muted">Chưa xác định</span>
                            }
                        </dd>

                        <dt class="col-sm-3 fw-bold">Giờ Thi Đấu:</dt>
                        <dd class="col-sm-9">
                            @if (Model.MatchTime.HasValue)
                            {
                                @Model.MatchTime.Value.ToString(@"hh\:mm")
                            }
                            else
                            {
                                <span>15:00</span>
                            }
                        </dd>

                        <dt class="col-sm-3 fw-bold">Địa Điểm Thi Đấu:</dt>
                        <dd class="col-sm-9">
                            @if (!string.IsNullOrEmpty(Model.Location))
                            {
                                @Model.Location
                            }
                            else
                            {
                                <span class="text-muted">Chưa xác định</span>
                            }
                        </dd>

                        <dt class="col-sm-3 fw-bold">Thời Gian Kết Thúc:</dt>
                        <dd class="col-sm-9">
                            @{
                                string endTimeDisplay = "15:15";
                                if (Model.Tournament != null && (Model.Tournament.Name.Contains("5v5") || Model.Tournament.Name.Contains("5 vs 5")))
                                {
                                    endTimeDisplay = "16:08";
                                }
                            }
                            @(matchEndTime?.ToString("HH:mm") ?? endTimeDisplay)
                        </dd>

                        <dt class="col-sm-3 fw-bold">Giải Đấu:</dt>
                        <dd class="col-sm-9">
                            @if (Model.Tournament != null)
                            {
                                <a asp-controller="Tournament" asp-action="Details" asp-route-id="@Model.TournamentId">
                                    @Html.DisplayFor(model => model.Tournament.Name)
                                </a>
                            }
                            else
                            {
                                <span class="text-muted">Chưa xác định</span>
                            }
                        </dd>
                    </dl>
                </div>
            </div>

            @if (matchSets != null && matchSets.Any())
            {
                <div class="card mb-4">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Kết Quả Các Hiệp Đấu</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-primary">
                                    <tr class="text-center">
                                        <th>Hiệp</th>
                                        <th>@Model.TeamA</th>
                                        <th>@Model.TeamB</th>
                                        <th class="d-none d-md-table-cell">Cầu Thủ Xuất Sắc</th>
                                        <th class="d-none d-md-table-cell">Đội</th>
                                        <th class="d-none d-md-table-cell">Điểm</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var set in matchSets)
                                    {
                                        <tr class="text-center">
                                            <td>@set.SetNumber</td>
                                            <td class="fw-bold">@set.ScoreTeamA</td>
                                            <td class="fw-bold">@set.ScoreTeamB</td>
                                            <td class="d-none d-md-table-cell">@set.BestPlayerName</td>
                                            <td class="d-none d-md-table-cell">@set.BestPlayerTeam</td>
                                            <td class="d-none d-md-table-cell">@set.BestPlayerPoints</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>

                            <!-- Hiển thị thông tin cầu thủ xuất sắc trên mobile -->
                            <div class="d-md-none mt-3">
                                <h6 class="text-center fw-bold">Cầu Thủ Xuất Sắc</h6>
                                @foreach (var set in matchSets)
                                {
                                    <div class="card mb-2">
                                        <div class="card-body p-2">
                                            <p class="mb-1"><strong>Hiệp @set.SetNumber:</strong> @set.BestPlayerName</p>
                                            <p class="mb-1"><strong>Đội:</strong> @set.BestPlayerTeam</p>
                                            <p class="mb-0"><strong>Điểm:</strong> @set.BestPlayerPoints</p>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            @if (playerScorings != null && playerScorings.Any())
            {
                <div class="card mb-4">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">Chi Tiết Ghi Điểm</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-warning">
                                    <tr class="text-center">
                                        <th>Cầu Thủ</th>
                                        <th>Đội</th>
                                        <th>Điểm Số</th>
                                        <th>Thời Gian</th>
                                        <th>Ghi Chú</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var scoring in playerScorings)
                                    {
                                        <tr class="text-center">
                                            <td>@scoring.Player.FullName (#@scoring.Player.Number)</td>
                                            <td>
                                                <span class="badge @(scoring.Player.Team.Name == Model.TeamA ? "bg-primary" : "bg-danger")">
                                                    @scoring.Player.Team.Name
                                                </span>
                                            </td>
                                            <td class="fw-bold">@scoring.Points</td>
                                            <td>@scoring.ScoringTime.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                            <td>@(string.IsNullOrEmpty(scoring.Notes) ? "-" : scoring.Notes)</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            @if (Model.Statistics != null && Model.Statistics.Any())
            {
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Thống Kê Cầu Thủ</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-success">
                                    <tr class="text-center">
                                        <th>Cầu Thủ</th>
                                        <th>Điểm</th>
                                        <th>Kiến Tạo</th>
                                        <th>Bắt Bóng</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var stat in Model.Statistics.OrderByDescending(s => s.Points))
                                    {
                                        <tr class="text-center">
                                            <td>
                                                <a asp-controller="Tournament" asp-action="FindPlayerByName" asp-route-playerName="@stat.PlayerName" asp-route-tournamentId="@Model.TournamentId" class="text-decoration-none">
                                                    @stat.PlayerName
                                                    <small class="d-block text-muted"><i class="bi bi-info-circle"></i> Nhấn để xem chi tiết</small>
                                                </a>
                                            </td>
                                            <td class="fw-bold">@stat.Points</td>
                                            <td>@stat.Assists</td>
                                            <td>@stat.Rebounds</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }

            <div class="mt-4">
                <div class="row">
                    @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                    {
                        <div class="col-md-6 mb-3 text-center text-md-start">
                            <a asp-action="ManagePlayerScoring" asp-route-id="@Model?.Id" class="btn btn-warning me-2 mb-2 mb-md-0">
                                <i class="bi bi-person-badge"></i> <span class="d-none d-sm-inline">Quản Lý Điểm Số</span><span class="d-inline d-sm-none">Điểm Số</span>
                            </a>
                            <a asp-action="UpdateScore" asp-route-id="@Model?.Id" class="btn btn-primary me-2 mb-2 mb-md-0">
                                <i class="bi bi-graph-up"></i> <span class="d-none d-sm-inline">Cập Nhật Kết Quả</span><span class="d-inline d-sm-none">Kết Quả</span>
                            </a>
                            <a asp-action="Edit" asp-route-id="@Model?.Id" class="btn btn-info">
                                <i class="bi bi-pencil"></i> <span class="d-none d-sm-inline">Chỉnh Sửa</span><span class="d-inline d-sm-none">Sửa</span>
                            </a>
                        </div>
                        <div class="col-md-6 text-center text-md-end">
                            <a asp-controller="Tournament" asp-action="Details" asp-route-id="@Model.TournamentId" class="btn btn-primary me-2 mb-2 mb-md-0">
                                <i class="bi bi-trophy"></i> <span class="d-none d-sm-inline">Xem Giải Đấu</span><span class="d-inline d-sm-none">Giải Đấu</span>
                            </a>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> <span class="d-none d-sm-inline">Quay Lại</span><span class="d-inline d-sm-none">Quay Lại</span>
                            </a>
                        </div>
                    }
                    else
                    {
                        <div class="col-12 text-center">
                            <a asp-controller="Tournament" asp-action="Details" asp-route-id="@Model.TournamentId" class="btn btn-primary me-2 mb-2 mb-md-0">
                                <i class="bi bi-trophy"></i> <span class="d-none d-sm-inline">Xem Giải Đấu</span><span class="d-inline d-sm-none">Giải Đấu</span>
                            </a>
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> <span class="d-none d-sm-inline">Quay Lại Danh Sách</span><span class="d-inline d-sm-none">Quay Lại</span>
                            </a>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
