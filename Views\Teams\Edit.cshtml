﻿@model WebQuanLyGiaiDau_NhomTD.Models.Team

@{
    ViewData["Title"] = "Chỉnh Sửa Đ<PERSON>";
}

<h1 class="text-center text-warning mt-4">✏️ Chỉnh <PERSON><PERSON>a Thông <PERSON> Đ<PERSON></h1>

<div class="container bg-light rounded shadow p-4 mt-4 mb-4">
    <form asp-action="Edit" enctype="multipart/form-data">
        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
        <input type="hidden" asp-for="TeamId" />

        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label asp-for="Name" class="form-label fw-bold">Tên <PERSON>:</label>
                    <input asp-for="Name" class="form-control" />
                    <span asp-validation-for="Name" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Coach" class="form-label fw-bold">Huấn <PERSON>:</label>
                    <input asp-for="Coach" class="form-control" />
                    <span asp-validation-for="Coach" class="text-danger"></span>
                </div>
            </div>

            <div class="col-md-4">
                <div class="mb-3 text-center">
                    <label class="form-label fw-bold d-block">Logo Hiện Tại:</label>
                    @if (!string.IsNullOrEmpty(Model.LogoUrl))
                    {
                        <img src="@Model.LogoUrl" alt="Logo đội bóng" class="img-thumbnail mb-2" style="max-height: 150px;" />
                    }
                    else
                    {
                        <div class="bg-light border rounded p-3 mb-2 text-muted">
                            <i class="bi bi-image"></i> Chưa có logo
                        </div>
                    }

                    <div class="mt-3">
                        <label class="form-label fw-bold">Thay Đổi Logo:</label>
                        <input type="file" name="logoFile" class="form-control" accept="image/*" />
                        <small class="text-muted">Để trống nếu không muốn thay đổi logo</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <input type="submit" value="💾 Lưu Thay Đổi" class="btn btn-success" />
            <a asp-action="Index" class="btn btn-outline-secondary">↩️ Quay Lại Danh Sách</a>
        </div>
    </form>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
