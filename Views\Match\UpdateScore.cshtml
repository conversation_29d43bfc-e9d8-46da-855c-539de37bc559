@model WebQuanLyGiaiDau_NhomTD.Models.Match

@{
    ViewData["Title"] = "Cập <PERSON><PERSON>t Quả Trận Đấu";
}

<h1 class="text-center text-primary mt-4">📊 Cập <PERSON>h<PERSON>t Kết Q<PERSON>ả Trậ<PERSON></h1>

<div class="container bg-light rounded shadow p-4 mt-4 mb-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Thông Tin Trận Đấu</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Gi<PERSON>i Đấu:</strong> @Model.Tournament?.Name</p>
                            <p><strong>Ngày <PERSON>hi <PERSON>:</strong> @Model.MatchDate.ToString("dd/MM/yyyy HH:mm")</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Trạng Thái:</strong> 
                                @{
                                    string statusClass = Model.CalculatedStatus switch
                                    {
                                        "Completed" => "bg-success",
                                        "InProgress" => "bg-danger",
                                        _ => "bg-primary"
                                    };
                                    string statusText = Model.CalculatedStatus switch
                                    {
                                        "Completed" => "Đã kết thúc",
                                        "InProgress" => "Đang diễn ra",
                                        _ => "Sắp diễn ra"
                                    };
                                }
                                <span class="badge @statusClass">@statusText</span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <form asp-action="UpdateScore">
        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
        <input type="hidden" asp-for="Id" />
        <input type="hidden" asp-for="TeamA" />
        <input type="hidden" asp-for="TeamB" />
        <input type="hidden" asp-for="MatchDate" />
        <input type="hidden" asp-for="TournamentId" />

        <div class="row mb-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Kết Quả Trận Đấu</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-5 text-center">
                                <h4 class="mb-3">@Model.TeamA</h4>
                                <div class="form-group">
                                    <label asp-for="ScoreTeamA" class="form-label fw-bold">Điểm Số:</label>
                                    <input asp-for="ScoreTeamA" class="form-control form-control-lg text-center" />
                                    <span asp-validation-for="ScoreTeamA" class="text-danger"></span>
                                </div>
                            </div>
                            <div class="col-md-2 d-flex align-items-center justify-content-center">
                                <h3 class="mb-0">VS</h3>
                            </div>
                            <div class="col-md-5 text-center">
                                <h4 class="mb-3">@Model.TeamB</h4>
                                <div class="form-group">
                                    <label asp-for="ScoreTeamB" class="form-label fw-bold">Điểm Số:</label>
                                    <input asp-for="ScoreTeamB" class="form-control form-control-lg text-center" />
                                    <span asp-validation-for="ScoreTeamB" class="text-danger"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <input type="submit" value="💾 Lưu Kết Quả" class="btn btn-success" />
            <div>
                <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-info me-2">🔍 Xem Chi Tiết</a>
                <a asp-action="Index" class="btn btn-outline-secondary">↩️ Quay Lại Danh Sách</a>
            </div>
        </div>
    </form>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
