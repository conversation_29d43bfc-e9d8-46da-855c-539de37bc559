﻿@model WebQuanLyGiaiDau_NhomTD.Models.Player

@{
    ViewData["Title"] = "Chỉnh Sửa Cầu Thủ";
}

<h1 class="text-center text-warning mt-4">✏️ Chỉnh <PERSON><PERSON><PERSON> Cầu <PERSON>ủ</h1>

<div class="container bg-light rounded shadow p-4 mt-4 mb-4">
    <form asp-action="Edit" enctype="multipart/form-data">
        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
        <input type="hidden" asp-for="PlayerId" />

        <div class="row">
            <div class="col-md-8">
                <div class="mb-3">
                    <label asp-for="FullName" class="form-label fw-bold">Họ Tên:</label>
                    <input asp-for="FullName" class="form-control" />
                    <span asp-validation-for="FullName" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Position" class="form-label fw-bold">Vị Trí:</label>
                    <input asp-for="Position" class="form-control" />
                    <span asp-validation-for="Position" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Number" class="form-label fw-bold">Số Áo:</label>
                    <input asp-for="Number" class="form-control" type="number" min="0" />
                    <span asp-validation-for="Number" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="TeamId" class="form-label fw-bold">Đội:</label>
                    <select asp-for="TeamId" class="form-select" asp-items="ViewBag.TeamId"></select>
                    <span asp-validation-for="TeamId" class="text-danger"></span>
                </div>
            </div>

            <div class="col-md-4">
                <div class="mb-3 text-center">
                    <label class="form-label fw-bold d-block">Ảnh Hiện Tại:</label>
                    @if (!string.IsNullOrEmpty(Model.ImageUrl))
                    {
                        <img src="@Model.ImageUrl" alt="Ảnh cầu thủ" class="img-thumbnail mb-2" style="max-height: 150px;" />
                    }
                    else
                    {
                        <div class="bg-light border rounded p-3 mb-2 text-muted">
                            <i class="bi bi-person"></i> Chưa có ảnh
                        </div>
                    }

                    <div class="mt-3">
                        <label class="form-label fw-bold">Thay Đổi Ảnh:</label>
                        <input type="file" name="imageFile" class="form-control" accept="image/*" />
                        <small class="text-muted">Để trống nếu không muốn thay đổi ảnh</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <input type="submit" value="💾 Lưu Thay Đổi" class="btn btn-success" />
            <a asp-action="Index" class="btn btn-outline-secondary">↩️ Quay Lại Danh Sách</a>
        </div>
    </form>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
