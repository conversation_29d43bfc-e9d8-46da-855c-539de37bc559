/* CSS cho trang Wiki Luật <PERSON>hi <PERSON>u */

/* Sidebar Navigation */
.rule-nav {
    position: sticky;
    top: 20px;
    z-index: 100;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rule-nav .list-group-item {
    border-left: 0;
    border-right: 0;
    padding: 0.75rem 1rem;
    transition: all 0.2s ease;
}

.rule-nav .list-group-item:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

.rule-nav .list-group-item.active {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
    border-left: 4px solid #0d6efd;
    font-weight: bold;
}

/* Section Styling */
.rule-section {
    padding-top: 70px;
    margin-top: -70px;
    scroll-margin-top: 70px;
}

.rule-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rule-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.rule-card .card-title {
    color: #0d6efd;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.rule-image {
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.rule-card:hover .rule-image {
    transform: scale(1.05);
}

/* Table Styling */
.table-differences {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table-differences th,
.table-differences td {
    vertical-align: middle;
    padding: 12px 15px;
}

.table-differences th {
    background-color: #0d6efd;
    color: white;
    font-weight: 600;
}

.table-differences tr:nth-child(even) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* FAQ Accordion Styling */
.accordion-item {
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.accordion-button:not(.collapsed) {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Back to Top Button */
.back-to-top {
    border-radius: 50px;
    padding: 0.5rem 1rem;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.2);
}

/* Responsive styles */
@media (max-width: 992px) {
    .rule-nav {
        position: relative;
        margin-bottom: 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }

    .rule-section {
        padding-top: 20px;
        margin-top: 0;
    }

    .rule-image {
        height: 150px;
    }
}

@media (max-width: 768px) {
    .card-body {
        padding: 1rem;
    }

    .table-differences th,
    .table-differences td {
        padding: 8px 10px;
        font-size: 0.9rem;
    }

    .accordion-button {
        padding: 0.75rem 1rem;
        font-size: 0.95rem;
    }
}

@media (max-width: 576px) {
    .rule-card {
        margin-bottom: 15px;
    }

    .table-differences th,
    .table-differences td {
        padding: 6px 8px;
        font-size: 0.85rem;
    }
}
