﻿@using Microsoft.AspNetCore.Identity
@using WebQuanLyGiaiDau_NhomTD.Models
@inject SignInManager<WebQuanLyGiaiDau_NhomTD.Models.ApplicationUser> SignInManager
@inject UserManager<WebQuanLyGiaiDau_NhomTD.Models.ApplicationUser> UserManager

<ul class="navbar-nav">
@if (SignInManager.IsSignedIn(User))
{
    var user = await UserManager.GetUserAsync(User);
    <li class="nav-item dropdown">
        <a class="nav-link dropdown-toggle user-profile-dropdown" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="bi bi-person-circle me-1"></i> @(user?.FullName ?? user?.UserName ?? "Người dùng")
        </a>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Index">
                    <i class="bi bi-person"></i> <PERSON><PERSON> sơ cá nhân
                </a>
            </li>
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/Email">
                    <i class="bi bi-envelope"></i> Email
                </a>
            </li>
            <li>
                <a class="dropdown-item" asp-area="Identity" asp-page="/Account/Manage/ChangePassword">
                    <i class="bi bi-key"></i> Mật khẩu
                </a>
            </li>
            <li><hr class="dropdown-divider"></li>
            <li>
                <form id="logoutForm" class="form-inline" asp-area="Identity" asp-page="/Account/Logout" asp-route-returnUrl="@Url.Action("Index", "Home", new { area = "" })">
                    <button id="logout" type="submit" class="dropdown-item">
                        <i class="bi bi-box-arrow-right"></i> Đăng xuất
                    </button>
                </form>
            </li>
        </ul>
    </li>
}
else
{
    <a class="btn btn-sports-outline me-2" id="login" asp-area="Identity" asp-page="/Account/Login">
        <i class="bi bi-box-arrow-in-right me-1"></i>Đăng nhập
    </a>
    <a class="btn btn-sports-primary" id="register" asp-area="Identity" asp-page="/Account/Register">
        <i class="bi bi-person-plus me-1"></i>Đăng ký
    </a>
}
</ul>
