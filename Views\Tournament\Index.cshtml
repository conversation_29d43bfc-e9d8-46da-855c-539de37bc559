@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.Tournament>
@using Microsoft.AspNetCore.Identity
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel
@inject SignInManager<WebQuanLyGiaiDau_NhomTD.Models.ApplicationUser> SignInManager
@inject UserManager<WebQuanLyGiaiDau_NhomTD.Models.ApplicationUser> UserManager

<h1 class="text-center text-primary mt-4">
    <i class="bi bi-trophy"></i> <PERSON>h S<PERSON>ch Giả<PERSON> Đấ<PERSON>
</h1>

@if (TempData["Message"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        @TempData["Message"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="row mb-3">
    <div class="col-md-6">
        <form asp-action="Index" method="get" class="d-flex">
            <div class="input-group">
                <input type="text" name="searchString" value="@ViewData["CurrentFilter"]" class="form-control" placeholder="Tìm kiếm giải đấu..." />
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-search"></i> Tìm
                </button>
                @if (!string.IsNullOrEmpty((string)ViewData["CurrentFilter"]))
                {
                    <a asp-action="Index" class="btn btn-outline-secondary">
                        <i class="bi bi-x-circle"></i> Xóa bộ lọc
                    </a>
                }
            </div>
        </form>
    </div>
    <div class="col-md-6 text-end">

        @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
        {
            <a asp-action="ManageTeamRegistrations" class="btn btn-info text-white me-2">
                <i class="bi bi-people-fill"></i> Quản lý đăng ký đội
            </a>
        }

        @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin) || User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_User))
        {
            <a asp-action="Create" class="btn btn-success">
                <i class="bi bi-plus-circle"></i> Tạo giải đấu mới
            </a>
        }
    </div>
</div>

<table class="table table-striped table-bordered shadow-sm">
    <thead class="table-dark text-center">
        <tr>
            <th>Tên Giải</th>
            <th>Mô Tả</th>
            <th>Địa Điểm</th>
            <th>Môn Thể Thao</th>
            <th>Ngày Bắt Đầu</th>
            <th>Ngày Kết Thúc</th>
            <th>Trạng Thái</th>
            <th>Hình Ảnh</th>
            <th>Thao Tác</th>
        </tr>
    </thead>
    <tbody class="align-middle text-center">
        @if (Model != null && Model.Any())
        {
            foreach (var item in Model)
            {
            <tr>
                <td>@item.Name</td>
                <td><span class="tournament-description">@item.Description</span></td>
                <td>
                    @if (!string.IsNullOrEmpty(item.Location))
                    {
                        @item.Location
                    }
                    else
                    {
                        <span class="text-muted">Không xác định</span>
                    }
                </td>
                <td>
                    @if (item.Sports != null)
                    {
                        <a asp-controller="Sports" asp-action="List" asp-route-sportsId="@item.SportsId" class="badge bg-primary text-decoration-none">
                            @item.Sports.Name
                        </a>
                    }
                    else
                    {
                        <span class="text-muted">Không xác định</span>
                    }
                </td>
                <td>
                    @if (item.StartDate != default(DateTime))
                    {
                        @item.StartDate.ToString("dd/MM/yyyy")
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </td>
                <td>
                    @if (item.EndDate != default(DateTime))
                    {
                        @item.EndDate.ToString("dd/MM/yyyy")
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </td>
                <td>
                    @{
                        string statusClass = item.CalculatedStatus switch
                        {
                            "Mở đăng ký" => "bg-success",
                            "Chưa mở đăng ký" => "bg-secondary",
                            "Kết thúc đăng ký" => "bg-warning",
                            "Giải đấu đang diễn ra" => "bg-danger",
                            "Giải đấu đã kết thúc" => "bg-dark",
                            _ => "bg-info"
                        };
                    }
                    <span class="badge @statusClass">@item.CalculatedStatus</span>
                </td>
                <td>
                    @if (!string.IsNullOrEmpty(item.ImageUrl))
                    {
                        <img src="@item.ImageUrl" alt="" class="img-thumbnail" style="height: 80px;" />
                    }
                    else
                    {
                        <span class="text-muted fst-italic">Không có ảnh</span>
                    }
                </td>
                <td>
                    <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-sm btn-info text-white">
                        <i class="bi bi-eye"></i> Xem
                    </a>
                    @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                    {
                        <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-sm btn-warning">
                            <i class="bi bi-pencil"></i> Sửa
                        </a>
                        <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-sm btn-danger">
                            <i class="bi bi-trash"></i> Xóa
                        </a>
                    }
                    @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_User) && item.CalculatedStatus == "Mở đăng ký")
                    {
                        <a asp-action="Register" asp-route-id="@item.Id" class="btn btn-sm btn-success">
                            <i class="bi bi-check-circle"></i> Đăng ký
                        </a>
                        <a asp-action="RegisterTeam" asp-route-id="@item.Id" class="btn btn-sm btn-primary">
                            <i class="bi bi-people-fill"></i> Đăng ký đội
                        </a>
                    }
                </td>
            </tr>
            }
        }
        else
        {
            <tr>
                <td colspan="6" class="text-center">Không tìm thấy giải đấu nào.</td>
            </tr>
        }
    </tbody>
</table>
