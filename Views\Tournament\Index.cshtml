@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.Tournament>
@using Microsoft.AspNetCore.Identity
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel
@inject SignInManager<WebQuanLyGiaiDau_NhomTD.Models.ApplicationUser> SignInManager
@inject UserManager<WebQuanLyGiaiDau_NhomTD.Models.ApplicationUser> UserManager

<style>
    .tournament-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 60px 0;
        margin-bottom: 40px;
        position: relative;
        overflow: hidden;
    }

    .tournament-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 2px, transparent 2px),
                         radial-gradient(circle at 80% 80%, rgba(255,255,255,0.1) 2px, transparent 2px),
                         radial-gradient(circle at 40% 60%, rgba(255,255,255,0.1) 1px, transparent 1px);
        animation: float 20s ease-in-out infinite;
    }

    .hero-stats {
        text-align: center;
        background: rgba(255,255,255,0.1);
        padding: 20px;
        border-radius: 15px;
        backdrop-filter: blur(10px);
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: white;
        margin-bottom: 5px;
    }

    .stat-label {
        font-size: 1rem;
        color: rgba(255,255,255,0.8);
        text-transform: uppercase;
        font-weight: 600;
    }

    @@keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }

    .tournament-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        margin-bottom: 30px;
        border: none;
    }

    .tournament-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .tournament-image {
        height: 200px;
        background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
        position: relative;
        overflow: hidden;
    }

    .tournament-image img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .tournament-image .sport-badge {
        position: absolute;
        top: 15px;
        left: 15px;
        background: rgba(255,255,255,0.9);
        padding: 8px 15px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 12px;
        color: #333;
    }

    .tournament-image .status-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 8px 15px;
        border-radius: 25px;
        font-weight: 600;
        font-size: 12px;
        color: white;
    }

    .tournament-content {
        padding: 25px;
    }

    .tournament-title {
        font-size: 1.4rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 10px;
        line-height: 1.3;
    }

    .tournament-description {
        color: #7f8c8d;
        font-size: 0.95rem;
        margin-bottom: 20px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }

    .tournament-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border-radius: 12px;
    }

    .meta-item {
        text-align: center;
        flex: 1;
    }

    .meta-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 5px;
        text-transform: uppercase;
        font-weight: 600;
    }

    .meta-value {
        font-size: 0.9rem;
        font-weight: 600;
        color: #2c3e50;
    }

    .tournament-actions {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
    }

    .btn-tournament {
        border-radius: 25px;
        padding: 8px 20px;
        font-weight: 600;
        font-size: 0.85rem;
        border: none;
        transition: all 0.3s ease;
        flex: 1;
        min-width: 100px;
    }

    .btn-tournament:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }

    .search-section {
        background: white;
        padding: 30px;
        border-radius: 20px;
        box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        margin-bottom: 40px;
    }

    .search-input {
        border-radius: 25px;
        border: 2px solid #e9ecef;
        padding: 12px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
    }

    .search-input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }

    .btn-search {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        color: white;
    }

    .btn-create {
        border-radius: 25px;
        padding: 12px 25px;
        font-weight: 600;
        background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
        border: none;
        color: white;
    }

    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .empty-state i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #dee2e6;
    }

    .status-open { background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%); }
    .status-closed { background: linear-gradient(135deg, #ff6b6b 0%, #ffa8a8 100%); }
    .status-upcoming { background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%); }
    .status-ongoing { background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); }
    .status-finished { background: linear-gradient(135deg, #4b6cb7 0%, #182848 100%); }

    /* Responsive Design */
    @@media (max-width: 768px) {
        .tournament-hero {
            padding: 40px 0;
            text-align: center;
        }

        .hero-stats {
            margin-top: 20px;
        }

        .search-section {
            padding: 20px;
        }

        .tournament-meta {
            flex-direction: column;
            gap: 10px;
        }

        .meta-item {
            flex: none;
        }

        .tournament-actions {
            flex-direction: column;
        }

        .btn-tournament {
            flex: none;
            width: 100%;
        }
    }

    @@media (max-width: 576px) {
        .tournament-hero h1 {
            font-size: 2rem;
        }

        .stat-number {
            font-size: 2rem;
        }

        .tournament-card {
            margin-bottom: 20px;
        }

        .tournament-content {
            padding: 20px;
        }
    }
</style>

<!-- Hero Section -->
<div class="tournament-hero">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <h1 class="display-4 fw-bold mb-3">
                    <i class="bi bi-trophy-fill me-3"></i>
                    Danh Sách Giải Đấu
                </h1>
                <p class="lead mb-0">Khám phá và tham gia các giải đấu thể thao hấp dẫn</p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="hero-stats">
                    <div class="stat-number">@(Model?.Count() ?? 0)</div>
                    <div class="stat-label">Giải đấu</div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Message"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <!-- Search and Actions Section -->
    <div class="search-section">
        <div class="row align-items-center">
            <div class="col-lg-6">
                <form asp-action="Index" method="get" class="d-flex">
                    <div class="input-group">
                        <input type="text" name="searchString" value="@ViewData["CurrentFilter"]"
                               class="form-control search-input" placeholder="🔍 Tìm kiếm giải đấu..." />
                        <button type="submit" class="btn btn-search">
                            <i class="bi bi-search"></i> Tìm kiếm
                        </button>
                        @if (!string.IsNullOrEmpty((string)ViewData["CurrentFilter"]))
                        {
                            <a asp-action="Index" class="btn btn-outline-secondary" style="border-radius: 25px;">
                                <i class="bi bi-x-circle"></i> Xóa bộ lọc
                            </a>
                        }
                    </div>
                </form>
            </div>
            <div class="col-lg-6 text-end">
                @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                {
                    <a asp-action="ManageTeamRegistrations" class="btn btn-info text-white me-2" style="border-radius: 25px;">
                        <i class="bi bi-people-fill"></i> Đăng ký đội
                    </a>
                }

                @if (User.Identity.IsAuthenticated)
                {
                    <a asp-action="Create" class="btn btn-create">
                        <i class="bi bi-plus-circle"></i> Tạo giải đấu mới
                    </a>
                }
            </div>
        </div>
    </div>

    <!-- Tournament Cards Grid -->
    <div class="row">
        @if (Model != null && Model.Any())
        {
            foreach (var item in Model)
            {
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="tournament-card">
                        <!-- Tournament Image -->
                        <div class="tournament-image">
                            @if (!string.IsNullOrEmpty(item.ImageUrl))
                            {
                                <img src="@item.ImageUrl" alt="@item.Name" />
                            }
                            else
                            {
                                <div class="d-flex align-items-center justify-content-center h-100">
                                    <i class="bi bi-trophy-fill text-white" style="font-size: 3rem;"></i>
                                </div>
                            }

                            <!-- Sport Badge -->
                            @if (item.Sports != null)
                            {
                                <div class="sport-badge">
                                    <i class="bi bi-award-fill me-1"></i>@item.Sports.Name
                                </div>
                            }

                            <!-- Status Badge -->
                            @{
                                string statusClass = item.CalculatedStatus switch
                                {
                                    "Mở đăng ký" => "status-open",
                                    "Chưa mở đăng ký" => "status-upcoming",
                                    "Kết thúc đăng ký" => "status-closed",
                                    "Giải đấu đang diễn ra" => "status-ongoing",
                                    "Giải đấu đã kết thúc" => "status-finished",
                                    _ => "status-upcoming"
                                };
                            }
                            <div class="status-badge @statusClass">
                                @item.CalculatedStatus
                            </div>
                        </div>

                        <!-- Tournament Content -->
                        <div class="tournament-content">
                            <h3 class="tournament-title">@item.Name</h3>

                            @if (!string.IsNullOrEmpty(item.Description))
                            {
                                <p class="tournament-description">@item.Description</p>
                            }

                            <!-- Tournament Meta Info -->
                            <div class="tournament-meta">
                                <div class="meta-item">
                                    <div class="meta-label">Bắt đầu</div>
                                    <div class="meta-value">
                                        @if (item.StartDate != default(DateTime))
                                        {
                                            @item.StartDate.ToString("dd/MM")
                                        }
                                        else
                                        {
                                            <span class="text-muted">TBA</span>
                                        }
                                    </div>
                                </div>
                                <div class="meta-item">
                                    <div class="meta-label">Kết thúc</div>
                                    <div class="meta-value">
                                        @if (item.EndDate != default(DateTime))
                                        {
                                            @item.EndDate.ToString("dd/MM")
                                        }
                                        else
                                        {
                                            <span class="text-muted">TBA</span>
                                        }
                                    </div>
                                </div>
                                <div class="meta-item">
                                    <div class="meta-label">Địa điểm</div>
                                    <div class="meta-value">
                                        @if (!string.IsNullOrEmpty(item.Location))
                                        {
                                            @(item.Location.Length > 10 ? item.Location.Substring(0, 10) + "..." : item.Location)
                                        }
                                        else
                                        {
                                            <span class="text-muted">TBA</span>
                                        }
                                    </div>
                                </div>
                            </div>

                            <!-- Tournament Actions -->
                            <div class="tournament-actions">
                                <a asp-action="Details" asp-route-id="@item.Id" class="btn btn-tournament btn-info text-white">
                                    <i class="bi bi-eye"></i> Xem chi tiết
                                </a>

                                @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                                {
                                    <a asp-action="Edit" asp-route-id="@item.Id" class="btn btn-tournament btn-warning">
                                        <i class="bi bi-pencil"></i> Sửa
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@item.Id" class="btn btn-tournament btn-danger">
                                        <i class="bi bi-trash"></i> Xóa
                                    </a>
                                }

                                @if (User.Identity.IsAuthenticated && !User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin) && item.CalculatedStatus == "Mở đăng ký")
                                {
                                    <a asp-action="Register" asp-route-id="@item.Id" class="btn btn-tournament btn-success">
                                        <i class="bi bi-check-circle"></i> Đăng ký
                                    </a>
                                    <a asp-action="RegisterTeam" asp-route-id="@item.Id" class="btn btn-tournament btn-primary">
                                        <i class="bi bi-people-fill"></i> Đăng ký đội
                                    </a>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        }
        else
        {
            <div class="col-12">
                <div class="empty-state">
                    <i class="bi bi-trophy"></i>
                    <h3>Không tìm thấy giải đấu nào</h3>
                    <p>Hiện tại chưa có giải đấu nào phù hợp với tìm kiếm của bạn.</p>
                    @if (User.Identity.IsAuthenticated)
                    {
                        <a asp-action="Create" class="btn btn-create mt-3">
                            <i class="bi bi-plus-circle"></i> Tạo giải đấu đầu tiên
                        </a>
                    }
                </div>
            </div>
        }
    </div>
</div>
