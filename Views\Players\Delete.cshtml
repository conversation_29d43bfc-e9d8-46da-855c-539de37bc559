﻿@model WebQuanLyGiaiDau_NhomTD.Models.Player

@{
    ViewData["Title"] = "Xóa Cầu Thủ";
}

<h1 class="text-center text-danger mt-4">🗑️ <PERSON><PERSON><PERSON> Cầu <PERSON>hủ</h1>
<h4 class="text-center text-secondary mb-4">Bạn có chắc chắn muốn xóa cầu thủ này không?</h4>

@if (ViewData["error"] != null)
{
    <div class="alert alert-danger">
        @ViewData["error"]
    </div>
}

<div class="container bg-light rounded shadow p-4 mb-4">
    <h5 class="text-primary">Thông Tin Cầu Thủ:</h5>
    <hr />
    <dl class="row">
        <dt class="col-sm-3 fw-bold">Họ Tên:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.FullName)</dd>

        <dt class="col-sm-3 fw-bold">Vị Trí:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.Position)</dd>

        <dt class="col-sm-3 fw-bold">Số Áo:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.Number)</dd>

        <dt class="col-sm-3 fw-bold">Đội:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.Team.Name)</dd>
    </dl>

    <form asp-action="Delete">
        <input type="hidden" asp-for="PlayerId" />
        <div class="d-flex justify-content-between mt-4">
            <input type="submit" value="🗑️ Xóa" class="btn btn-danger" />
            <a asp-action="Index" class="btn btn-outline-secondary">↩️ Quay Lại</a>
        </div>
    </form>
</div>
