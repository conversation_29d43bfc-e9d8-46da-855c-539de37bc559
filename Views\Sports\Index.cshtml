﻿@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.Sports>
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel

@{
    ViewData["Title"] = "<PERSON><PERSON><PERSON>";
}

<style>
    /* Ultra Modern Sports Page Styling */
    .sports-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        padding: 100px 0;
        position: relative;
        overflow: hidden;
        margin-bottom: 0;
        min-height: 60vh;
        display: flex;
        align-items: center;
    }

    .sports-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 20% 20%, rgba(255,255,255,0.08) 2px, transparent 2px),
            radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 2px, transparent 2px);
        background-size: 100px 100px, 150px 150px;
        animation: float 40s ease-in-out infinite;
    }

    @@keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }

    .hero-content {
        position: relative;
        z-index: 2;
    }

    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        color: white;
        margin-bottom: 25px;
        text-shadow: 0 4px 16px rgba(0,0,0,0.3);
        letter-spacing: -1px;
        line-height: 1.1;
    }

    .hero-subtitle {
        font-size: 1.4rem;
        color: rgba(255,255,255,0.95);
        margin-bottom: 0;
        font-weight: 300;
        letter-spacing: 0.5px;
        text-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }

    .hero-stats {
        background: rgba(255,255,255,0.1);
        padding: 40px 30px;
        border-radius: 25px;
        backdrop-filter: blur(20px);
        text-align: center;
        border: 2px solid rgba(255,255,255,0.2);
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .stat-number {
        font-size: 3rem;
        font-weight: 700;
        color: white;
        margin-bottom: 15px;
        display: block;
        position: relative;
        z-index: 2;
        text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }

    .stat-label {
        font-size: 1.2rem;
        color: rgba(255,255,255,0.9);
        text-transform: uppercase;
        font-weight: 700;
        letter-spacing: 2px;
        position: relative;
        z-index: 2;
    }

    /* Search Section */
    .search-section {
        background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
        padding: 50px 0;
        box-shadow: 0 10px 40px rgba(0,0,0,0.08);
        position: relative;
    }

    .search-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(102, 126, 234, 0.3), transparent);
    }

    .search-input {
        border: 3px solid transparent;
        border-radius: 60px;
        padding: 20px 30px;
        font-size: 1.2rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(white, white) padding-box,
                   linear-gradient(135deg, #667eea, #764ba2, #f093fb) border-box;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.1);
        position: relative;
    }

    .search-input:focus {
        outline: none;
        transform: translateY(-2px);
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.2);
        background: linear-gradient(white, white) padding-box,
                   linear-gradient(135deg, #f093fb, #667eea, #764ba2) border-box;
    }

    .search-input::placeholder {
        color: #adb5bd;
        font-weight: 500;
    }

    .view-toggle {
        background: #f8f9fa;
        border-radius: 50px;
        padding: 5px;
        border: 2px solid #e9ecef;
    }

    .view-btn {
        border: none;
        background: transparent;
        padding: 10px 20px;
        border-radius: 50px;
        transition: all 0.3s ease;
        font-weight: 600;
    }

    .view-btn.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    /* Sports Cards */
    .sports-container {
        padding: 80px 0;
        background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 50%, #f1f3f4 100%);
        position: relative;
    }

    .sports-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image:
            radial-gradient(circle at 10% 20%, rgba(102, 126, 234, 0.03) 0%, transparent 50%),
            radial-gradient(circle at 90% 80%, rgba(240, 147, 251, 0.03) 0%, transparent 50%);
        pointer-events: none;
    }

    .sport-card {
        background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
        border-radius: 30px;
        padding: 50px 25px;
        text-align: center;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        border: 2px solid rgba(255,255,255,0.8);
        box-shadow:
            0 20px 40px rgba(0,0,0,0.08),
            0 0 0 1px rgba(255,255,255,0.5),
            inset 0 1px 0 rgba(255,255,255,0.9);
        position: relative;
        overflow: hidden;
        height: 100%;
        cursor: pointer;
        backdrop-filter: blur(10px);
    }

    .sport-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        opacity: 0;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        z-index: 1;
    }

    .sport-card:hover::before {
        opacity: 0.9;
    }

    .sport-card:hover {
        transform: translateY(-10px);
        box-shadow:
            0 20px 40px rgba(102, 126, 234, 0.2),
            0 0 0 1px rgba(255,255,255,0.8),
            inset 0 1px 0 rgba(255,255,255,1);
        border-color: rgba(255,255,255,1);
    }

    .sport-content {
        position: relative;
        z-index: 2;
        transition: all 0.4s ease;
    }

    .sport-card:hover .sport-content {
        color: white;
    }

    .sport-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 3.5rem;
        transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        box-shadow:
            0 15px 35px rgba(0,0,0,0.1),
            inset 0 2px 0 rgba(255,255,255,0.3),
            inset 0 -2px 0 rgba(0,0,0,0.1);
    }

    .sport-icon img {
        max-width: 80px;
        max-height: 80px;
        object-fit: contain;
        transition: all 0.4s ease;
        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
    }

    .sport-card:hover .sport-icon {
        transform: scale(1.05);
        background: rgba(255,255,255,0.2) !important;
        box-shadow:
            0 15px 30px rgba(255,255,255,0.15),
            inset 0 2px 0 rgba(255,255,255,0.3),
            inset 0 -2px 0 rgba(255,255,255,0.1);
    }

    .sport-card:hover .sport-icon img {
        filter: brightness(0) invert(1) drop-shadow(0 2px 8px rgba(255,255,255,0.2));
    }

    .sport-title {
        font-size: 1.8rem;
        font-weight: 800;
        margin-bottom: 15px;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        letter-spacing: -0.5px;
        position: relative;
    }



    .sport-description {
        color: #6c757d;
        font-size: 1.1rem;
        margin-bottom: 30px;
        transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        line-height: 1.6;
        font-weight: 400;
    }

    .sport-card:hover .sport-description {
        color: rgba(255,255,255,0.9);
    }

    .sport-action {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 12px 30px;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    }

    .sport-card:hover .sport-action {
        background: rgba(255,255,255,0.9);
        color: #667eea;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255,255,255,0.3);
    }

    /* Admin Actions */
    .admin-actions {
        position: absolute;
        top: 15px;
        right: 15px;
        z-index: 3;
        opacity: 0;
        transition: all 0.3s ease;
    }

    .sport-card:hover .admin-actions {
        opacity: 1;
    }

    .admin-btn {
        background: rgba(255,255,255,0.9);
        border: none;
        width: 35px;
        height: 35px;
        border-radius: 50%;
        margin-left: 5px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        color: #495057;
    }

    .admin-btn:hover {
        background: white;
        transform: scale(1.1);
    }

    .admin-btn.edit:hover {
        color: #ffc107;
    }

    .admin-btn.delete:hover {
        color: #dc3545;
    }

    /* Empty State */
    .empty-state {
        text-align: center;
        padding: 80px 20px;
        background: white;
        border-radius: 25px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .empty-icon {
        font-size: 5rem;
        color: #dee2e6;
        margin-bottom: 30px;
    }

    .empty-title {
        font-size: 2rem;
        font-weight: 700;
        color: #495057;
        margin-bottom: 15px;
    }

    .empty-text {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 30px;
    }

    .btn-create {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 15px 35px;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1.1rem;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }

    .btn-create:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    /* Sport Icon Gradients */
    .gradient-basketball {
        background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
        box-shadow: 0 0 15px rgba(255, 107, 53, 0.3);
    }
    .gradient-football {
        background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
        box-shadow: 0 0 15px rgba(78, 205, 196, 0.3);
    }
    .gradient-volleyball {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        box-shadow: 0 0 15px rgba(240, 147, 251, 0.3);
    }
    .gradient-tennis {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        box-shadow: 0 0 15px rgba(79, 172, 254, 0.3);
    }
    .gradient-badminton {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        box-shadow: 0 0 15px rgba(67, 233, 123, 0.3);
    }
    .gradient-default {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        box-shadow: 0 0 15px rgba(102, 126, 234, 0.3);
    }

    /* Enhanced Responsive Design */
    @@media (max-width: 992px) {
        .hero-title {
            font-size: 3.2rem;
        }

        .sport-card {
            padding: 40px 20px;
        }

        .sport-icon {
            width: 100px;
            height: 100px;
        }
    }

    @@media (max-width: 768px) {
        .sports-hero {
            padding: 60px 0;
            min-height: 50vh;
            text-align: center;
        }

        .hero-title {
            font-size: 2.8rem;
            letter-spacing: -1px;
        }

        .hero-subtitle {
            font-size: 1.2rem;
        }

        .hero-stats {
            margin-top: 40px;
            padding: 30px 20px;
        }

        .stat-number {
            font-size: 3rem;
        }

        .search-section {
            padding: 40px 0;
        }

        .search-input {
            padding: 18px 25px;
            font-size: 1.1rem;
        }

        .sports-container {
            padding: 60px 0;
        }

        .sport-card {
            margin-bottom: 40px;
            padding: 40px 20px;
        }

        .sport-icon {
            width: 100px;
            height: 100px;
            font-size: 3rem;
        }

        .sport-title {
            font-size: 1.6rem;
        }

        .sport-description {
            font-size: 1rem;
        }
    }

    @@media (max-width: 576px) {
        .sports-hero {
            padding: 40px 0;
            min-height: 40vh;
        }

        .hero-title {
            font-size: 2.2rem;
            letter-spacing: -0.5px;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .hero-stats {
            padding: 25px 15px;
        }

        .stat-number {
            font-size: 2.5rem;
        }

        .stat-label {
            font-size: 1rem;
            letter-spacing: 1px;
        }

        .search-section {
            padding: 30px 0;
        }

        .search-input {
            padding: 15px 20px;
            font-size: 1rem;
        }

        .sports-container {
            padding: 40px 0;
        }

        .sport-card {
            padding: 35px 20px;
            border-radius: 25px;
        }

        .sport-icon {
            width: 90px;
            height: 90px;
            font-size: 2.5rem;
            margin-bottom: 25px;
        }

        .sport-icon img {
            max-width: 60px;
            max-height: 60px;
        }

        .sport-title {
            font-size: 1.4rem;
            margin-bottom: 12px;
        }

        .sport-description {
            font-size: 0.95rem;
            margin-bottom: 25px;
        }

        .sport-action {
            padding: 12px 28px;
            font-size: 1rem;
        }

        .btn-create {
            padding: 12px 25px;
            font-size: 1rem;
        }
    }

    /* Ultra-wide screens */
    @@media (min-width: 1400px) {
        .hero-title {
            font-size: 4.5rem;
        }

        .sport-card {
            padding: 60px 30px;
        }

        .sport-icon {
            width: 140px;
            height: 140px;
            font-size: 4rem;
        }

        .sport-title {
            font-size: 2rem;
        }
    }
</style>

<!-- Hero Section -->
<div class="sports-hero">
    <div class="container">
        <div class="row align-items-center hero-content">
            <div class="col-lg-8">
                <h1 class="hero-title">
                    <i class="bi bi-trophy-fill me-3"></i>
                    Môn Thể Thao
                </h1>
                <p class="hero-subtitle">Khám phá và tham gia các môn thể thao yêu thích của bạn</p>
            </div>
            <div class="col-lg-4 text-end">
                <div class="hero-stats">
                    <span class="stat-number">@(Model?.Count() ?? 0)</span>
                    <div class="stat-label">Môn thể thao</div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Section -->
<div class="search-section">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="position-relative">
                    <input type="text" id="searchInput" class="form-control search-input"
                           placeholder="🔍 Tìm kiếm môn thể thao...">
                </div>
            </div>
            <div class="col-lg-4 text-end mt-3 mt-lg-0">
                @if (User.IsInRole(SD.Role_Admin))
                {
                    <a asp-controller="Sports" asp-action="Create" class="btn btn-create">
                        <i class="bi bi-plus-circle me-2"></i>Thêm môn thể thao
                    </a>
                }
            </div>
        </div>
    </div>
</div>

<!-- Sports Container -->
<div class="sports-container">
    <div class="container">
        @if (TempData["SuccessMessage"] != null)
        {
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        }

        <!-- Sports Grid -->
        <div class="row g-4">
            @if (Model != null && Model.Any())
            {
                foreach (var sport in Model)
                {
                    <div class="col-lg-4 col-md-6 sport-item" data-name="@sport.Name.ToLower()">
                        <div class="sport-card">
                            <!-- Admin Actions -->
                            @if (User.IsInRole(SD.Role_Admin))
                            {
                                <div class="admin-actions">
                                    <a asp-action="Edit" asp-route-id="@sport.Id" class="admin-btn edit" title="Chỉnh sửa">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a asp-action="Delete" asp-route-id="@sport.Id" class="admin-btn delete" title="Xóa">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            }

                            <div class="sport-content">
                                <!-- Sport Icon -->
                                <div class="sport-icon @GetSportGradient(sport.Name)">
                                    @if (!string.IsNullOrEmpty(sport.ImageUrl))
                                    {
                                        <img src="@sport.ImageUrl" alt="@sport.Name" />
                                    }
                                    else
                                    {
                                        <i class="@GetSportIcon(sport.Name)"></i>
                                    }
                                </div>

                                <!-- Sport Info -->
                                <h3 class="sport-title">@sport.Name</h3>
                                <p class="sport-description">Khám phá các giải đấu @sport.Name hấp dẫn</p>

                                <!-- Action Button -->
                                <a asp-controller="Sports" asp-action="List" asp-route-sportsId="@sport.Id" class="sport-action">
                                    <i class="bi bi-arrow-right me-2"></i>Xem giải đấu
                                </a>
                            </div>
                        </div>
                    </div>
                }
            }
            else
            {
                <div class="col-12">
                    <div class="empty-state">
                        <i class="bi bi-trophy empty-icon"></i>
                        <h3 class="empty-title">Chưa có môn thể thao nào</h3>
                        @if (User.IsInRole(SD.Role_Admin))
                        {
                            <p class="empty-text">Hãy thêm môn thể thao đầu tiên để bắt đầu tạo các giải đấu.</p>
                            <a asp-controller="Sports" asp-action="Create" class="btn-create">
                                <i class="bi bi-plus-circle me-2"></i>Thêm môn thể thao đầu tiên
                            </a>
                        }
                        else
                        {
                            <p class="empty-text">Vui lòng liên hệ quản trị viên để thêm môn thể thao.</p>
                        }
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@functions {
    string GetSportGradient(string sportName)
    {
        return sportName.ToLower() switch
        {
            "bóng rổ" or "basketball" => "gradient-basketball",
            "bóng đá" or "football" or "soccer" => "gradient-football",
            "bóng chuyền" or "volleyball" => "gradient-volleyball",
            "tennis" => "gradient-tennis",
            "cầu lông" or "badminton" => "gradient-badminton",
            _ => "gradient-default"
        };
    }

    string GetSportIcon(string sportName)
    {
        return sportName.ToLower() switch
        {
            "bóng rổ" or "basketball" => "bi bi-circle-fill",
            "bóng đá" or "football" or "soccer" => "bi bi-circle",
            "bóng chuyền" or "volleyball" => "bi bi-hexagon-fill",
            "tennis" => "bi bi-circle-half",
            "cầu lông" or "badminton" => "bi bi-diamond-fill",
            _ => "bi bi-trophy-fill"
        };
    }
}

@section Scripts {
    <script>
        $(document).ready(function () {
            // Search functionality
            $("#searchInput").on("keyup", function () {
                var value = $(this).val().toLowerCase();
                $(".sport-item").filter(function () {
                    $(this).toggle($(this).data("name").indexOf(value) > -1);
                });
            });

            // Add hover effects for sport cards
            $(".sport-card").hover(
                function() {
                    $(this).addClass("hovered");
                },
                function() {
                    $(this).removeClass("hovered");
                }
            );

            // Smooth scroll for any anchor links
            $('a[href^="#"]').on('click', function(event) {
                var target = $(this.getAttribute('href'));
                if( target.length ) {
                    event.preventDefault();
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                }
            });

            // Add loading animation when clicking sport cards
            $(".sport-action").on("click", function() {
                $(this).html('<i class="bi bi-arrow-repeat me-2 spin"></i>Đang tải...');
            });
        });
    </script>

    <style>
        .spin {
            animation: spin 1s linear infinite;
        }

        @@keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
}


