@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.News>

@{
    ViewData["Title"] = "Tin tức thể thao";
}

<div class="container py-5">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mb-0"><i class="bi bi-newspaper me-2"></i>Tin tức thể thao</h1>
        
        @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
        {
            <a asp-action="Create" class="btn btn-success">
                <i class="bi bi-plus-circle me-2"></i>Thêm tin tức mới
            </a>
        }
    </div>

    <!-- Tìm kiếm -->
    <div class="card mb-4">
        <div class="card-body">
            <form asp-action="Index" method="get" class="row g-3">
                <div class="col-md-10">
                    <div class="input-group">
                        <span class="input-group-text"><i class="bi bi-search"></i></span>
                        <input type="text" class="form-control" name="searchString" value="@ViewData["CurrentFilter"]" placeholder="Tìm kiếm tin tức...">
                    </div>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">Tìm kiếm</button>
                </div>
            </form>
        </div>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i> @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <i class="bi bi-info-circle me-2"></i> Không có tin tức nào.
        </div>
    }
    else
    {
        <!-- Tin tức nổi bật -->
        @if (Model.Any(n => n.IsFeatured))
        {
            <h2 class="mb-4">Tin tức nổi bật</h2>
            <div class="row mb-5">
                @foreach (var item in Model.Where(n => n.IsFeatured).Take(3))
                {
                    <div class="col-md-4 mb-4">
                        <div class="card news-card h-100">
                            <div class="news-img-container">
                                <img src="@(string.IsNullOrEmpty(item.ImageUrl) ? "/images/news/default.jpg" : item.ImageUrl)" alt="@item.Title" class="news-img">
                                <div class="news-badge">
                                    <i class="bi bi-star-fill me-1"></i>Nổi bật
                                </div>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <div class="news-date mb-2">
                                    <i class="bi bi-calendar-event me-1"></i>@item.PublishDate.ToString("dd/MM/yyyy HH:mm")
                                    <span class="ms-2"><i class="bi bi-eye me-1"></i>@item.ViewCount</span>
                                </div>
                                <h5 class="news-title">@item.Title</h5>
                                <p class="news-summary">@item.Summary</p>
                                <div class="mt-auto pt-3 d-flex justify-content-between">
                                    <a asp-action="Details" asp-route-id="@item.NewsId" class="btn btn-outline-primary">
                                        <i class="bi bi-book me-1"></i>Đọc tiếp
                                    </a>
                                    @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                                    {
                                        <div>
                                            <a asp-action="Edit" asp-route-id="@item.NewsId" class="btn btn-outline-warning">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <a asp-action="Delete" asp-route-id="@item.NewsId" class="btn btn-outline-danger">
                                                <i class="bi bi-trash"></i>
                                            </a>
                                        </div>
                                    }
                                </div>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }

        <!-- Tất cả tin tức -->
        <h2 class="mb-4">Tất cả tin tức</h2>
        <div class="row">
            @foreach (var item in Model)
            {
                <div class="col-md-4 mb-4">
                    <div class="card news-card h-100">
                        <div class="news-img-container">
                            <img src="@(string.IsNullOrEmpty(item.ImageUrl) ? "/images/news/default.jpg" : item.ImageUrl)" alt="@item.Title" class="news-img">
                            @if (item.IsFeatured)
                            {
                                <div class="news-badge">
                                    <i class="bi bi-star-fill me-1"></i>Nổi bật
                                </div>
                            }
                        </div>
                        <div class="card-body d-flex flex-column">
                            <div class="news-date mb-2">
                                <i class="bi bi-calendar-event me-1"></i>@item.PublishDate.ToString("dd/MM/yyyy HH:mm")
                                <span class="ms-2"><i class="bi bi-eye me-1"></i>@item.ViewCount</span>
                            </div>
                            <h5 class="news-title">@item.Title</h5>
                            <p class="news-summary">@item.Summary</p>
                            <div class="mt-auto pt-3 d-flex justify-content-between">
                                <a asp-action="Details" asp-route-id="@item.NewsId" class="btn btn-outline-primary">
                                    <i class="bi bi-book me-1"></i>Đọc tiếp
                                </a>
                                @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                                {
                                    <div>
                                        <a asp-action="Edit" asp-route-id="@item.NewsId" class="btn btn-outline-warning">
                                            <i class="bi bi-pencil"></i>
                                        </a>
                                        <a asp-action="Delete" asp-route-id="@item.NewsId" class="btn btn-outline-danger">
                                            <i class="bi bi-trash"></i>
                                        </a>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>

<style>
    .news-card {
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border: none;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }
    
    .news-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
    }
    
    .news-img-container {
        height: 200px;
        overflow: hidden;
        position: relative;
    }
    
    .news-img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.5s ease;
    }
    
    .news-card:hover .news-img {
        transform: scale(1.05);
    }
    
    .news-date {
        font-size: 0.8rem;
        color: #6c757d;
    }
    
    .news-title {
        font-weight: 600;
        margin-bottom: 0.5rem;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .news-summary {
        color: #6c757d;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        overflow: hidden;
    }
    
    .news-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        background-color: var(--primary-color, #0066cc);
        color: white;
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
</style>
