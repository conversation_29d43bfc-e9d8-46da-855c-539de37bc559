﻿@model WebQuanLyGiaiDau_NhomTD.Models.Team
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel

@{
    ViewData["Title"] = "Chi Tiết Đội Bóng";
}

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i> @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<h1 class="text-center text-info mt-4">📋 Chi Tiết Đội Bóng</h1>

<div class="container bg-light shadow rounded p-4 mt-4 mb-4">
    <div class="row">
        <div class="col-md-4 text-center mb-3">
            @if (!string.IsNullOrEmpty(Model.LogoUrl))
            {
                <img src="@Model.LogoUrl" alt="Logo @Model.Name" class="img-fluid rounded" style="max-height: 200px;" />
            }
            else
            {
                <div class="bg-light d-flex align-items-center justify-content-center p-3" style="height: 200px;">
                    <i class="bi bi-shield-fill text-secondary" style="font-size: 5rem;"></i>
                </div>
            }
        </div>
        <div class="col-md-8">
            <h5 class="text-primary mb-3">Thông Tin Đội:</h5>
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Tên Đội:</dt>
                <dd class="col-sm-8">
                    @if (!string.IsNullOrEmpty(Model.Name))
                    {
                        @Html.DisplayFor(model => model.Name)
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </dd>

                <dt class="col-sm-4 fw-bold">Huấn Luyện Viên:</dt>
                <dd class="col-sm-8">
                    @if (!string.IsNullOrEmpty(Model.Coach))
                    {
                        @Html.DisplayFor(model => model.Coach)
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </dd>
            </dl>
        </div>
    </div>

    <div class="mt-4 d-flex justify-content-between">
        @if (User.IsInRole(SD.Role_Admin))
        {
            <a asp-action="Edit" asp-route-id="@Model?.TeamId" class="btn btn-warning">
                ✏️ Chỉnh Sửa
            </a>
        }
        <a asp-action="Index" class="btn btn-outline-secondary">
            ↩️ Quay Lại Danh Sách
        </a>
    </div>
</div>

<div class="container bg-light shadow rounded p-4 mt-4 mb-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h5 class="text-primary">Danh Sách Cầu Thủ:</h5>
        @if (User.IsInRole(SD.Role_Admin))
        {
            <a asp-action="AddPlayer" asp-route-teamId="@Model?.TeamId" class="btn btn-success">
                ➕ Thêm Cầu Thủ
            </a>
        }
    </div>

    @if (Model != null && Model.Players != null && Model.Players.Any())
    {
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead class="table-dark">
                    <tr>
                        <th>Ảnh</th>
                        <th>Số Áo</th>
                        <th>Họ Tên</th>
                        <th>Vị Trí</th>
                        <th>Hành Động</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var player in Model.Players)
                    {
                        <tr>
                            <td>
                                @if (!string.IsNullOrEmpty(player.ImageUrl))
                                {
                                    <img src="@player.ImageUrl" alt="Ảnh @player.FullName" class="rounded-circle" style="height: 40px; width: 40px; object-fit: cover;" />
                                }
                                else
                                {
                                    <img src="/images/Group 1364.png" alt="Default" class="rounded-circle" style="height: 40px; width: 40px; object-fit: cover;" />
                                }
                            </td>
                            <td class="text-center">@player.Number</td>
                            <td>
                                @if (!string.IsNullOrEmpty(player.FullName))
                                {
                                    @player.FullName
                                }
                                else
                                {
                                    <span class="text-muted">Chưa xác định</span>
                                }
                            </td>
                            <td>
                                @if (!string.IsNullOrEmpty(player.Position))
                                {
                                    @player.Position
                                }
                                else
                                {
                                    <span class="text-muted">Chưa xác định</span>
                                }
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a asp-controller="Players" asp-action="Details" asp-route-id="@player.PlayerId" class="btn btn-sm btn-info me-1">
                                        🔍 Xem
                                    </a>
                                    @if (User.IsInRole(SD.Role_Admin))
                                    {
                                        <a asp-action="EditPlayer" asp-route-id="@player.PlayerId" asp-route-teamId="@(Model?.TeamId)" class="btn btn-sm btn-warning me-1">
                                            ✏️ Sửa
                                        </a>
                                        <a asp-action="DeletePlayer" asp-route-id="@player.PlayerId" asp-route-teamId="@(Model?.TeamId)" class="btn btn-sm btn-danger">
                                            🗑️ Xóa
                                        </a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
    }
    else
    {
        <div class="alert alert-info">
            Đội bóng này chưa có cầu thủ nào.
        </div>
    }
</div>
