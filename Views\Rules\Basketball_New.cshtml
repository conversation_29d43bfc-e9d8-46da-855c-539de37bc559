@{
    ViewData["Title"] = "Luật Bóng Rổ";
    var basketballRules = ViewBag.BasketballRules as List<WebQuanLyGiaiDau_NhomTD.Models.RuleCategory>;
    var differences = ViewBag.Differences as List<WebQuanLyGiaiDau_NhomTD.Models.RuleDifference>;
    var faqs = ViewBag.FAQs as List<WebQuanLyGiaiDau_NhomTD.Models.FAQ>;
}


<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar Navigation -->
        <div class="col-md-3 mb-4">
            <div class="card shadow-sm rule-nav">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><PERSON><PERSON><PERSON></h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        @foreach (var category in basketballRules)
                        {
                            <a href="#<EMAIL>" class="list-group-item list-group-item-action">
                                @category.Name
                            </a>
                        }
                        <a href="#differences" class="list-group-item list-group-item-action">
                            So sánh 3x3 và 5x5
                        </a>
                        <a href="#faq" class="list-group-item list-group-item-action">
                            Câu hỏi thường gặp (FAQ)
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-md-9">
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h1 class="text-center text-primary mb-4">Luật Bóng Rổ</h1>
                    <p class="lead text-center">Luật bóng rổ chính thức theo tiêu chuẩn FIBA</p>

                    <div class="alert alert-info">
                        <i class="bi bi-info-circle-fill me-2"></i>
                        Trang này cung cấp thông tin về luật bóng rổ chính thức được sử dụng trong các giải đấu quốc tế và trong nước. Các luật được tổng hợp từ quy định của Liên đoàn Bóng rổ Quốc tế (FIBA).
                    </div>
                </div>
            </div>

            <!-- Rule Categories -->
            @foreach (var category in basketballRules)
            {
                <div id="<EMAIL>" class="rule-section">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h2 class="h4 mb-0">@category.Name</h2>
                        </div>
                        <div class="card-body">
                            <p class="lead">@category.Description</p>

                            <div class="row">
                                @foreach (var rule in category.Rules)
                                {
                                    <div class="col-md-6 mb-4">
                                        <div class="card h-100 rule-card">
                                            <div class="card-body">
                                                <h5 class="card-title">@rule.Title</h5>
                                                <p class="card-text">@rule.Content</p>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Differences between 3x3 and 5x5 -->
            <div id="differences" class="rule-section">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h2 class="h4 mb-0">So sánh Bóng rổ 3x3 và 5x5</h2>
                    </div>
                    <div class="card-body">
                        <p class="lead">Bóng rổ 3x3 và 5x5 có nhiều điểm khác biệt về luật chơi. Dưới đây là bảng so sánh chi tiết:</p>

                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-differences">
                                <thead class="table-primary">
                                    <tr>
                                        <th>Tiêu chí</th>
                                        <th>Bóng rổ 5x5</th>
                                        <th>Bóng rổ 3x3</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var diff in differences)
                                    {
                                        <tr>
                                            <td class="fw-bold">@diff.Category</td>
                                            <td>@diff.Rule5v5</td>
                                            <td>@diff.Rule3x3</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>Lưu ý:</strong> Bóng rổ 3x3 là môn thể thao Olympic chính thức từ năm 2021 và có bộ luật riêng được FIBA ban hành.
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div id="faq" class="rule-section">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h2 class="h4 mb-0">Câu hỏi thường gặp (FAQ)</h2>
                    </div>
                    <div class="card-body">
                        <p class="lead">Dưới đây là những câu hỏi thường gặp về luật bóng rổ:</p>

                        <div class="accordion" id="faqAccordion">
                            @foreach (var faq in faqs)
                            {
                                <div class="accordion-item">
                                    <h3 class="accordion-header" id="heading@(faq.Id)">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse@(faq.Id)" aria-expanded="false" aria-controls="collapse@(faq.Id)">
                                            @faq.Question
                                        </button>
                                    </h3>
                                    <div id="collapse@(faq.Id)" class="accordion-collapse collapse" aria-labelledby="heading@(faq.Id)" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            @faq.Answer
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back to top button -->
            <div class="text-center mb-4">
                <a href="#" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-up-circle"></i> Về đầu trang
                </a>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Add special intro for 3x3 basketball section
        document.addEventListener('DOMContentLoaded', function() {
            const category3x3 = document.querySelector('#category-8 .card-body');
            if (category3x3) {
                const introDiv = document.createElement('div');
                introDiv.className = 'alert alert-warning mb-4';
                introDiv.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-trophy-fill fs-3 me-3"></i>
                        <div>
                            <h5 class="alert-heading">Bóng rổ 3x3 - Môn thể thao Olympic</h5>
                            <p class="mb-0">Bóng rổ 3x3 đã trở thành môn thể thao Olympic chính thức từ Thế vận hội Tokyo 2020. Với nhịp độ nhanh, không gian nhỏ hơn và luật chơi đơn giản hơn, bóng rổ 3x3 đang ngày càng phổ biến trên toàn thế giới.</p>
                        </div>
                    </div>
                `;

                // Insert after the lead paragraph
                const leadParagraph = category3x3.querySelector('.lead');
                if (leadParagraph && leadParagraph.nextElementSibling) {
                    category3x3.insertBefore(introDiv, leadParagraph.nextElementSibling);
                } else if (leadParagraph) {
                    leadParagraph.after(introDiv);
                }
            }
        });
    </script>
}
