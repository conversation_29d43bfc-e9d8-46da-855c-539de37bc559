@model WebQuanLyGiaiDau_NhomTD.Models.Player

@{
    ViewData["Title"] = "Thêm Cầu <PERSON>hủ";
}

<h1 class="text-center text-primary mt-4">⚽ Thêm Cầu <PERSON>hủ Mớ<PERSON></h1>
<h4 class="text-center text-secondary mb-4">Đ<PERSON><PERSON>: @ViewData["TeamName"]</h4>

@if (TempData["SuccessMessage"] != null)
{
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="bi bi-check-circle-fill me-2"></i> @TempData["SuccessMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

@if (TempData["ErrorMessage"] != null)
{
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="bi bi-exclamation-triangle-fill me-2"></i> @TempData["ErrorMessage"]
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
}

<div class="row justify-content-center">
    <div class="col-md-6">
        <form asp-action="AddPlayer" method="post" enctype="multipart/form-data" class="bg-light p-4 rounded shadow">
            <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
            <input type="hidden" asp-for="TeamId" value="@ViewData["TeamId"]" />

            <div class="mb-3">
                <label asp-for="FullName" class="form-label fw-semibold">Họ Tên</label>
                <input asp-for="FullName" class="form-control" placeholder="Nhập họ tên cầu thủ..." />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Position" class="form-label fw-semibold">Vị Trí</label>
                <input asp-for="Position" class="form-control" placeholder="Vị trí thi đấu..." />
                <span asp-validation-for="Position" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Number" class="form-label fw-semibold">Số Áo</label>
                <input asp-for="Number" class="form-control" placeholder="Số áo..." min="0" max="99" type="number" />
                <span asp-validation-for="Number" class="text-danger"></span>
                <small class="text-muted">Số áo phải từ 0 đến 99</small>
            </div>

            <div class="mb-3">
                <label class="form-label fw-semibold">Ảnh Cầu Thủ</label>
                <input type="file" name="imageFile" class="form-control" accept="image/*" />
                <small class="text-muted">Chọn ảnh đại diện cho cầu thủ (không bắt buộc)</small>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-success">
                    💾 Lưu Cầu Thủ
                </button>
                <a asp-action="Details" asp-route-id="@ViewData["TeamId"]" class="btn btn-outline-secondary">
                    ↩️ Quay Lại
                </a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script>
        // Xác nhận trước khi submit form
        $(document).ready(function () {
            $('form').submit(function (e) {
                // Kiểm tra các trường bắt buộc
                var fullName = $('#FullName').val();
                var position = $('#Position').val();
                var number = $('#Number').val();

                if (!fullName || !position || !number) {
                    // Nếu thiếu thông tin, hiển thị thông báo và ngăn submit
                    alert('Vui lòng điền đầy đủ thông tin cầu thủ (Họ tên, Vị trí, Số áo)');
                    e.preventDefault();
                    return false;
                }

                // Kiểm tra số áo hợp lệ
                if (number < 0 || number > 99) {
                    alert('Số áo phải từ 0 đến 99');
                    e.preventDefault();
                    return false;
                }

                // Kiểm tra kích thước file ảnh nếu có
                var fileInput = $('input[name="imageFile"]')[0];
                if (fileInput && fileInput.files.length > 0) {
                    var fileSize = fileInput.files[0].size; // kích thước tính bằng byte
                    var maxSize = 5 * 1024 * 1024; // 5MB

                    if (fileSize > maxSize) {
                        alert('Kích thước ảnh quá lớn. Vui lòng chọn ảnh nhỏ hơn 5MB.');
                        e.preventDefault();
                        return false;
                    }
                }

                return true;
            });

            // Hiển thị xem trước ảnh khi chọn file
            $('input[name="imageFile"]').change(function() {
                if (this.files && this.files[0]) {
                    var reader = new FileReader();

                    reader.onload = function(e) {
                        // Tạo hoặc cập nhật phần tử xem trước
                        var previewContainer = $('.image-preview');
                        if (previewContainer.length === 0) {
                            // Tạo mới nếu chưa có
                            var newPreview = '<div class="image-preview mt-2 mb-2"><p>Ảnh đã chọn:</p><img src="' + e.target.result + '" class="img-thumbnail" style="max-height: 150px;" /></div>';
                            $('input[name="imageFile"]').after(newPreview);
                        } else {
                            // Cập nhật nếu đã có
                            previewContainer.find('img').attr('src', e.target.result);
                        }
                    }

                    reader.readAsDataURL(this.files[0]);
                }
            });
        });
    </script>
}
