﻿@model WebQuanLyGiaiDau_NhomTD.Models.Player
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel

@{
    ViewData["Title"] = "Chi Tiết Cầu Thủ";
    var playerStats = ViewData["PlayerStatistics"] as List<WebQuanLyGiaiDau_NhomTD.Models.Statistic>;
}

<h1 class="text-center text-info mt-4">📋 Chi Tiết Cầu Thủ</h1>

<div class="container bg-light shadow rounded p-4 mt-4 mb-4">
    <div class="row">
        <div class="col-md-4 text-center mb-3">
            @if (!string.IsNullOrEmpty(Model.ImageUrl))
            {
                <img src="@Model.ImageUrl" alt="Ảnh @Model.FullName" class="img-thumbnail" style="max-height: 250px;" />
            }
            else
            {
                <img src="/images/Group 1364.png" alt="Default" class="img-thumbnail" style="max-height: 250px;" />
            }
        </div>
        <div class="col-md-8">
            <h5 class="text-primary mb-3">Thông Tin Cầu Thủ:</h5>
            <dl class="row">
                <dt class="col-sm-3 fw-bold">Họ Tên:</dt>
                <dd class="col-sm-9">
                    @if (!string.IsNullOrEmpty(Model.FullName))
                    {
                        @Html.DisplayFor(model => model.FullName)
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </dd>

                <dt class="col-sm-3 fw-bold">Vị Trí:</dt>
                <dd class="col-sm-9">
                    @if (!string.IsNullOrEmpty(Model.Position))
                    {
                        @Html.DisplayFor(model => model.Position)
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </dd>

                <dt class="col-sm-3 fw-bold">Số Áo:</dt>
                <dd class="col-sm-9">@Html.DisplayFor(model => model.Number)</dd>

                <dt class="col-sm-3 fw-bold">Đội Bóng:</dt>
                <dd class="col-sm-9">
                    @if (Model.Team != null)
                    {
                        @Html.DisplayFor(model => model.Team.Name)
                    }
                    else
                    {
                        <span class="text-muted">Chưa xác định</span>
                    }
                </dd>
            </dl>
        </div>
    </div>

    <div class="mt-4">
        <h5 class="text-primary mb-3">Thống Kê Trung Bình:</h5>
        @if (ViewData["TotalGames"] != null)
        {
            <div class="row text-center">
                <div class="col-md-3 mb-3">
                    <div class="card bg-primary text-white">
                        <div class="card-body">
                            <h5 class="card-title">Số Trận</h5>
                            <p class="card-text display-6">@ViewData["TotalGames"]</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-success text-white">
                        <div class="card-body">
                            <h5 class="card-title">Điểm/Trận</h5>
                            <p class="card-text display-6">
                                @{
                                    double avgPoints = 0;
                                    if (ViewData["AvgPoints"] != null && ViewData["AvgPoints"].ToString() != null)
                                    {
                                        if (double.TryParse(ViewData["AvgPoints"].ToString(), out double pointsResult))
                                        {
                                            avgPoints = Math.Round(pointsResult, 1);
                                        }
                                    }
                                }
                                @avgPoints
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-info text-white">
                        <div class="card-body">
                            <h5 class="card-title">Kiến Tạo/Trận</h5>
                            <p class="card-text display-6">
                                @{
                                    double avgAssists = 0;
                                    if (ViewData["AvgAssists"] != null && ViewData["AvgAssists"].ToString() != null)
                                    {
                                        if (double.TryParse(ViewData["AvgAssists"].ToString(), out double assistsResult))
                                        {
                                            avgAssists = Math.Round(assistsResult, 1);
                                        }
                                    }
                                }
                                @avgAssists
                            </p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card bg-warning text-dark">
                        <div class="card-body">
                            <h5 class="card-title">Bắt Bóng/Trận</h5>
                            <p class="card-text display-6">
                                @{
                                    double avgRebounds = 0;
                                    if (ViewData["AvgRebounds"] != null && ViewData["AvgRebounds"].ToString() != null)
                                    {
                                        if (double.TryParse(ViewData["AvgRebounds"].ToString(), out double reboundsResult))
                                        {
                                            avgRebounds = Math.Round(reboundsResult, 1);
                                        }
                                    }
                                }
                                @avgRebounds
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="alert alert-info">
                Cầu thủ này chưa có thống kê nào.
            </div>
        }
    </div>

    @if (playerStats != null && playerStats.Any())
    {
        <div class="mt-4">
            <h5 class="text-primary mb-3">Thống Kê Theo Trận:</h5>
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Trận Đấu</th>
                            <th class="text-center">Điểm</th>
                            <th class="text-center">Kiến Tạo</th>
                            <th class="text-center">Bắt Bóng</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var stat in playerStats)
                        {
                            <tr>
                                <td>
                                    @if (!string.IsNullOrEmpty(stat.Match.TeamA) && !string.IsNullOrEmpty(stat.Match.TeamB))
                                    {
                                        <a asp-controller="Match" asp-action="Details" asp-route-id="@stat.MatchId" class="text-decoration-none">
                                            @(stat.Match.TeamA + " vs " + stat.Match.TeamB)
                                            <small class="d-block text-muted"><i class="bi bi-info-circle"></i> Nhấn để xem chi tiết trận đấu</small>
                                        </a>
                                    }
                                    else
                                    {
                                        <span class="text-muted">Chưa xác định</span>
                                    }
                                </td>
                                <td class="text-center">@stat.Points</td>
                                <td class="text-center">@stat.Assists</td>
                                <td class="text-center">@stat.Rebounds</td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        </div>
    }

    <div class="mt-4 d-flex justify-content-between">
        @if (User.IsInRole(SD.Role_Admin))
        {
            <a asp-action="Edit" asp-route-id="@Model?.PlayerId" class="btn btn-warning">
                ✏️ Chỉnh Sửa
            </a>
        }
        <a asp-action="Index" class="btn btn-outline-secondary">
            ↩️ Quay Lại Danh Sách
        </a>
    </div>
</div>
