@model WebQuanLyGiaiDau_NhomTD.Models.Sports

@{
    ViewData["Title"] = "Chỉnh Sửa Môn <PERSON>ể <PERSON>hao";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-pencil-square"></i> Chỉnh Sửa Môn <PERSON>hao
                    </h4>
                </div>
                <div class="card-body">
                    <form asp-action="Edit" method="post" enctype="multipart/form-data">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="Id" />

                        <div class="mb-3">
                            <label asp-for="Name" class="form-label">Tên Môn Thể <PERSON>hao</label>
                            <input asp-for="Name" class="form-control" />
                            <span asp-validation-for="Name" class="text-danger"></span>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Hình Ảnh Hiện Tại</label>
                            <div class="text-center mb-3">
                                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                {
                                    <img src="@Model.ImageUrl" alt="@Model.Name" class="img-thumbnail" style="max-height: 200px;" />
                                }
                                else
                                {
                                    <div class="bg-light p-4 rounded text-muted">
                                        <i class="bi bi-image display-4"></i>
                                        <p>Không có hình ảnh</p>
                                    </div>
                                }
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="imageUrl" class="form-label">Thay Đổi Hình Ảnh</label>
                            <input type="file" id="imageUrl" name="imageUrl" class="form-control" accept="image/*" />
                            <div class="form-text">Để trống nếu không muốn thay đổi hình ảnh.</div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Quay Lại
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> Lưu Thay Đổi
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
