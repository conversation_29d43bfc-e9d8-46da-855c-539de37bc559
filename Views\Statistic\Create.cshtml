@model WebQuanLyGiaiDau_NhomTD.Models.Statistic

@{
    ViewData["Title"] = "Thêm Thống Kê Mới";
}

<h1 class="text-center text-success mt-4">📊 Thêm Thống Kê Mới</h1>
<hr class="mb-4" />

<div class="row justify-content-center">
    <div class="col-md-6">
        <form asp-action="Create" class="bg-light p-4 rounded shadow">
            <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

            <div class="mb-3">
                <label asp-for="PlayerName" class="form-label fw-semibold">T<PERSON>n <PERSON></label>
                <input asp-for="PlayerName" class="form-control" placeholder="Nhập tên cầu thủ..." />
                <span asp-validation-for="PlayerName" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Points" class="form-label fw-semibold"><PERSON><PERSON><PERSON><PERSON></label>
                <input asp-for="Points" class="form-control" type="number" min="0" />
                <span asp-validation-for="Points" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Assists" class="form-label fw-semibold">Kiến Tạo</label>
                <input asp-for="Assists" class="form-control" type="number" min="0" />
                <span asp-validation-for="Assists" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Rebounds" class="form-label fw-semibold">Bắt Bóng</label>
                <input asp-for="Rebounds" class="form-control" type="number" min="0" />
                <span asp-validation-for="Rebounds" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="MatchId" class="form-label fw-semibold">Trận Đấu</label>
                <select asp-for="MatchId" class="form-select" asp-items="ViewBag.MatchId">
                    <option value="">-- Chọn Trận Đấu --</option>
                </select>
                <span asp-validation-for="MatchId" class="text-danger"></span>
            </div>

            <div class="d-flex justify-content-between mt-4">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-plus-circle"></i> Thêm Thống Kê
                </button>
                <a asp-action="Index" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i> Quay Lại
                </a>
            </div>
        </form>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
