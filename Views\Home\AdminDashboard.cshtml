@{
    ViewData["Title"] = "Bảng Điều Khiển Admin";
}

<div class="container py-5">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-primary text-white shadow">
                <div class="card-body">
                    <h2 class="card-title">
                        <i class="bi bi-speedometer2 me-2"></i>Bảng Điều Khiển Admin
                    </h2>
                    <p class="card-text">Chào mừng đến với bảng điều khiển quản trị. Từ đây bạn có thể quản lý tất cả các khía cạnh của hệ thống.</p>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Quản lý giải đấu -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-primary text-white me-3">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <h5 class="card-title mb-0">Quản Lý Giải Đấu</h5>
                    </div>
                    <p class="card-text">Tạo, chỉnh sửa và quản lý các giải đấu thể thao.</p>
                    <div class="mt-auto">
                        <a asp-controller="Tournament" asp-action="Index" class="btn btn-outline-primary">
                            <i class="bi bi-arrow-right"></i> Truy cập
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quản lý đội bóng -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-success text-white me-3">
                            <i class="bi bi-people-fill"></i>
                        </div>
                        <h5 class="card-title mb-0">Quản Lý Đội Bóng</h5>
                    </div>
                    <p class="card-text">Quản lý thông tin về các đội bóng tham gia giải đấu.</p>
                    <div class="mt-auto">
                        <a asp-controller="Teams" asp-action="Index" class="btn btn-outline-success">
                            <i class="bi bi-arrow-right"></i> Truy cập
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quản lý cầu thủ -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-info text-white me-3">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <h5 class="card-title mb-0">Quản Lý Cầu Thủ</h5>
                    </div>
                    <p class="card-text">Quản lý thông tin về các cầu thủ trong các đội bóng.</p>
                    <div class="mt-auto">
                        <a asp-controller="Players" asp-action="Index" class="btn btn-outline-info">
                            <i class="bi bi-arrow-right"></i> Truy cập
                        </a>
                    </div>
                </div>
            </div>
        </div>



        <!-- Quản lý môn thể thao -->
        <div class="col-md-6 col-lg-4">
            <div class="card h-100 shadow-sm border-0 hover-card">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-box bg-danger text-white me-3">
                            <i class="bi bi-dribbble"></i>
                        </div>
                        <h5 class="card-title mb-0">Quản Lý Môn Thể Thao</h5>
                    </div>
                    <p class="card-text">Quản lý các môn thể thao có trong hệ thống.</p>
                    <div class="mt-auto">
                        <a asp-controller="Sports" asp-action="Index" class="btn btn-outline-danger">
                            <i class="bi bi-arrow-right"></i> Truy cập
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .icon-box {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
    }

    .hover-card {
        transition: transform 0.3s, box-shadow 0.3s;
    }

    .hover-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1) !important;
    }
</style>
