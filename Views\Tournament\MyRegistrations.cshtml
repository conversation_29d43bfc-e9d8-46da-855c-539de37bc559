@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.TournamentRegistration>

@{
    ViewData["Title"] = "Giải đấu đã đăng ký";
}

<div class="container mt-4">
    <h1 class="text-center text-primary mb-4">Giải đấu đã đăng ký</h1>

    <div class="mb-3">
        <a asp-action="Index" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Quay lại danh sách giải đấu
        </a>
    </div>

    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Message"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <h4 class="alert-heading">Chưa có đăng ký!</h4>
            <p>Bạn chưa đăng ký tham gia giải đấu nào.</p>
            <hr>
            <p class="mb-0">Hãy quay lại danh sách giải đấu để đăng ký tham gia.</p>
        </div>
    }
    else
    {
        <div class="row">
            @foreach (var registration in Model)
            {
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0">@registration.Tournament.Name</h5>
                        </div>
                        <div class="card-body">
                            @if (!string.IsNullOrEmpty(registration.Tournament.ImageUrl))
                            {
                                <img src="@registration.Tournament.ImageUrl" class="card-img-top mb-3" alt="@registration.Tournament.Name" style="height: 150px; object-fit: cover;">
                            }

                            <p class="card-text">@registration.Tournament.Description</p>

                            <div class="d-flex justify-content-between mb-2">
                                <small class="text-muted">Ngày bắt đầu:</small>
                                <small class="text-muted">@registration.Tournament.StartDate.ToString("dd/MM/yyyy")</small>
                            </div>

                            <div class="d-flex justify-content-between mb-3">
                                <small class="text-muted">Ngày kết thúc:</small>
                                <small class="text-muted">@registration.Tournament.EndDate.ToString("dd/MM/yyyy")</small>
                            </div>

                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>Trạng thái:</strong>
                                    @switch (registration.Status)
                                    {
                                        case "Pending":
                                            <span class="badge bg-warning text-dark">Đang chờ duyệt</span>
                                            break;
                                        case "Approved":
                                            <span class="badge bg-success">Đã duyệt</span>
                                            break;
                                        case "Rejected":
                                            <span class="badge bg-danger">Từ chối</span>
                                            break;
                                        default:
                                            <span class="badge bg-secondary">@registration.Status</span>
                                            break;
                                    }
                                </div>
                                <small class="text-muted">Đăng ký: @registration.RegistrationDate.ToString("dd/MM/yyyy")</small>
                            </div>
                        </div>
                        <div class="card-footer">
                            <a asp-action="Details" asp-route-id="@registration.TournamentId" class="btn btn-sm btn-info text-white">
                                <i class="bi bi-eye"></i> Xem chi tiết giải đấu
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    }
</div>
