@{
    ViewData["Title"] = "Luậ<PERSON> Thi Đấu";
    var basketballRules = ViewBag.BasketballRules as List<WebQuanLyGiaiDau_NhomTD.Models.RuleCategory>;
    var differences = ViewBag.Differences as List<WebQuanLyGiaiDau_NhomTD.Models.RuleDifference>;
    var faqs = ViewBag.FAQs as List<WebQuanLyGiaiDau_NhomTD.Models.FAQ>;
}

<div class="container-fluid mt-4">
    <div class="row">
        <!-- Sidebar Navigation - Fixed on desktop, scrollable on mobile -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow-sm rule-nav">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">M<PERSON><PERSON></h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        <a href="#overview" class="list-group-item list-group-item-action fw-bold">
                            <i class="bi bi-info-circle me-2"></i>Tổng quan
                        </a>
                        
                        <!-- Basketball Rules -->
                        <div class="list-group-item bg-light fw-bold">
                            <i class="bi bi-dribbble me-2"></i>Luật Bóng Rổ
                        </div>
                        
                        @foreach (var category in basketballRules)
                        {
                            <a href="#<EMAIL>" class="list-group-item list-group-item-action ps-4">
                                <i class="bi bi-dot me-2"></i>@category.Name
                            </a>
                        }
                        
                        <a href="#differences" class="list-group-item list-group-item-action ps-4">
                            <i class="bi bi-dot me-2"></i>So sánh 3x3 và 5x5
                        </a>
                        
                        <a href="#faq" class="list-group-item list-group-item-action ps-4">
                            <i class="bi bi-dot me-2"></i>Câu hỏi thường gặp
                        </a>
                        
                        <!-- Placeholder for other sports -->
                        <div class="list-group-item bg-light text-muted">
                            <i class="bi bi-plus-circle me-2"></i>Các môn thể thao khác (sắp ra mắt)
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Back to top button - Visible only on mobile -->
            <div class="d-lg-none text-center mt-3">
                <a href="#" class="btn btn-sm btn-primary back-to-top">
                    <i class="bi bi-arrow-up-circle me-1"></i>Về đầu trang
                </a>
            </div>
        </div>

        <!-- Main Content -->
        <div class="col-lg-9">
            <!-- Overview Section -->
            <div id="overview" class="rule-section">
                <div class="card shadow-sm mb-4">
                    <div class="card-body">
                        <h1 class="text-center text-primary mb-4">Luật Thi Đấu Thể Thao</h1>
                        <p class="lead text-center">Trang wiki luật thi đấu chính thức cho các môn thể thao</p>

                        <div class="alert alert-info">
                            <div class="d-flex">
                                <div class="me-3">
                                    <i class="bi bi-info-circle-fill fs-3"></i>
                                </div>
                                <div>
                                    <h5 class="alert-heading">Về trang luật thi đấu</h5>
                                    <p class="mb-0">Trang này cung cấp thông tin chi tiết về luật thi đấu chính thức được sử dụng trong các giải đấu. Các luật được tổng hợp từ quy định của các liên đoàn thể thao quốc tế và trong nước. Sử dụng mục lục bên trái để điều hướng đến các phần khác nhau của trang.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Basketball Section Header -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h2 class="h3 mb-0"><i class="bi bi-dribbble me-2"></i>Luật Bóng Rổ</h2>
                </div>
                <div class="card-body">
                    <p class="lead">Luật bóng rổ chính thức theo tiêu chuẩn FIBA</p>
                    <div class="alert alert-light border">
                        <p class="mb-0">Bóng rổ là môn thể thao đồng đội trong nhà, được chơi giữa hai đội với mỗi đội có năm cầu thủ (bóng rổ 5x5) hoặc ba cầu thủ (bóng rổ 3x3). Mục tiêu là ghi điểm bằng cách ném bóng vào rổ của đối phương và ngăn đội đối phương ghi điểm.</p>
                    </div>
                </div>
            </div>

            <!-- Rule Categories -->
            @foreach (var category in basketballRules)
            {
                <div id="<EMAIL>" class="rule-section">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-light">
                            <h3 class="h5 mb-0">@category.Name</h3>
                        </div>
                        <div class="card-body">
                            <p class="lead">@category.Description</p>

                            <div class="row">
                                @foreach (var rule in category.Rules)
                                {
                                    <div class="col-md-12 mb-4">
                                        <div class="card h-100 rule-card">
                                            <div class="card-body">
                                                <h4 class="card-title h5">@rule.Title</h4>
                                                <p class="card-text">@rule.Content</p>
                                            </div>
                                        </div>
                                    </div>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            }

            <!-- Differences between 3x3 and 5x5 -->
            <div id="differences" class="rule-section">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h3 class="h5 mb-0">So sánh Bóng rổ 3x3 và 5x5</h3>
                    </div>
                    <div class="card-body">
                        <p class="lead">Bóng rổ 3x3 và 5x5 có nhiều điểm khác biệt về luật chơi. Dưới đây là bảng so sánh chi tiết:</p>

                        <div class="table-responsive">
                            <table class="table table-striped table-bordered table-differences">
                                <thead class="table-primary">
                                    <tr>
                                        <th>Tiêu chí</th>
                                        <th>Bóng rổ 5x5</th>
                                        <th>Bóng rổ 3x3</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var diff in differences)
                                    {
                                        <tr>
                                            <td class="fw-bold">@diff.Category</td>
                                            <td>@diff.Rule5v5</td>
                                            <td>@diff.Rule3x3</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="alert alert-warning mt-3">
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                            <strong>Lưu ý:</strong> Bóng rổ 3x3 là môn thể thao Olympic chính thức từ năm 2021 và có bộ luật riêng được FIBA ban hành.
                        </div>
                    </div>
                </div>
            </div>

            <!-- FAQ Section -->
            <div id="faq" class="rule-section">
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-light">
                        <h3 class="h5 mb-0">Câu hỏi thường gặp (FAQ)</h3>
                    </div>
                    <div class="card-body">
                        <p class="lead">Dưới đây là những câu hỏi thường gặp về luật bóng rổ:</p>

                        <div class="accordion" id="faqAccordion">
                            @foreach (var faq in faqs)
                            {
                                <div class="accordion-item">
                                    <h4 class="accordion-header" id="heading@(faq.Id)">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse@(faq.Id)" aria-expanded="false" aria-controls="collapse@(faq.Id)">
                                            @faq.Question
                                        </button>
                                    </h4>
                                    <div id="collapse@(faq.Id)" class="accordion-collapse collapse" aria-labelledby="heading@(faq.Id)" data-bs-parent="#faqAccordion">
                                        <div class="accordion-body">
                                            @faq.Answer
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            </div>

            <!-- Back to top button - Fixed at bottom right on desktop -->
            <div class="d-none d-lg-block position-fixed bottom-0 end-0 p-3" style="z-index: 5">
                <a href="#" class="btn btn-primary back-to-top">
                    <i class="bi bi-arrow-up-circle me-1"></i>Về đầu trang
                </a>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();

                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);

                if (targetElement) {
                    window.scrollTo({
                        top: targetElement.offsetTop - 20,
                        behavior: 'smooth'
                    });
                }
            });
        });

        // Highlight active section in the navigation
        const sections = document.querySelectorAll('.rule-section');
        const navLinks = document.querySelectorAll('.list-group-item-action');

        window.addEventListener('scroll', () => {
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                
                if (pageYOffset >= sectionTop - 100) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === `#${current}`) {
                    link.classList.add('active');
                }
            });
        });
    </script>
}
