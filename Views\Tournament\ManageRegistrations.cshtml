@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.TournamentRegistration>

@{
    ViewData["Title"] = "Quản lý đăng ký giải đấu";
}

<div class="container mt-4">
    <h1 class="text-center text-primary mb-4">Quản lý đăng ký giải đấu</h1>

    <div class="mb-3">
        <a asp-action="Index" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Quay lại danh sách giải đấu
        </a>
    </div>

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <h4 class="alert-heading">Chưa có đăng ký!</h4>
            <p>Hiện tại chưa có người dùng nào đăng ký tham gia giải đấu.</p>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0"><PERSON><PERSON> sách đăng ký</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Người dùng</th>
                                <th>Giải đấu</th>
                                <th>Ngày đăng ký</th>
                                <th>Ghi chú</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var registration in Model)
                            {
                                <tr>
                                    <td>@registration.Id</td>
                                    <td>@registration.User.FullName</td>
                                    <td>@registration.Tournament.Name</td>
                                    <td>@registration.RegistrationDate.ToString("dd/MM/yyyy")</td>
                                    <td>
                                        @if (string.IsNullOrEmpty(registration.Notes))
                                        {
                                            <span class="text-muted fst-italic">Không có</span>
                                        }
                                        else
                                        {
                                            @registration.Notes
                                        }
                                    </td>
                                    <td>
                                        @switch (registration.Status)
                                        {
                                            case "Pending":
                                                <span class="badge bg-warning text-dark">Đang chờ duyệt</span>
                                                break;
                                            case "Approved":
                                                <span class="badge bg-success">Đã duyệt</span>
                                                break;
                                            case "Rejected":
                                                <span class="badge bg-danger">Từ chối</span>
                                                break;
                                            default:
                                                <span class="badge bg-secondary">@registration.Status</span>
                                                break;
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <form asp-action="UpdateRegistrationStatus" method="post">
                                                <input type="hidden" name="id" value="@registration.Id" />
                                                
                                                @if (registration.Status != "Approved")
                                                {
                                                    <button type="submit" name="status" value="Approved" class="btn btn-sm btn-success me-1">
                                                        <i class="bi bi-check-circle"></i> Duyệt
                                                    </button>
                                                }
                                                
                                                @if (registration.Status != "Rejected")
                                                {
                                                    <button type="submit" name="status" value="Rejected" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-x-circle"></i> Từ chối
                                                    </button>
                                                }
                                                
                                                @if (registration.Status != "Pending" && registration.Status != "Approved" && registration.Status != "Rejected")
                                                {
                                                    <button type="submit" name="status" value="Pending" class="btn btn-sm btn-warning">
                                                        <i class="bi bi-clock"></i> Đặt chờ duyệt
                                                    </button>
                                                }
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
