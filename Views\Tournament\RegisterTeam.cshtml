@model WebQuanLyGiaiDau_NhomTD.Models.Tournament

@{
    ViewData["Title"] = "Đăng ký đội cho giải đấu";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h2 class="text-center">Đăng ký đội cho giải đấu</h2>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-4">
                            @if (!string.IsNullOrEmpty(Model.ImageUrl))
                            {
                                <img src="@Model.ImageUrl" class="img-fluid rounded" alt="@Model.Name">
                            }
                            else
                            {
                                <div class="bg-light p-4 text-center rounded">
                                    <span class="text-muted">Không có hình ảnh</span>
                                </div>
                            }
                        </div>
                        <div class="col-md-8">
                            <h3>@Model.Name</h3>
                            <p class="text-muted">@Model.Description</p>
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong>Ngày bắt đầu:</strong>
                                    <p>@Model.StartDate.ToString("dd/MM/yyyy")</p>
                                </div>
                                <div>
                                    <strong>Ngày kết thúc:</strong>
                                    <p>@Model.EndDate.ToString("dd/MM/yyyy")</p>
                                </div>
                            </div>
                            <div>
                                <strong>Môn thể thao:</strong>
                                <p>@(Model.Sports?.Name ?? "Không xác định")</p>
                            </div>
                        </div>
                    </div>

                    <form asp-action="RegisterTeam" method="post">
                        <input type="hidden" name="id" value="@Model.Id" />
                        
                        <div class="mb-3">
                            <label for="teamId" class="form-label">Chọn đội tham gia</label>
                            <select name="teamId" id="teamId" class="form-select" required>
                                <option value="">-- Chọn đội --</option>
                                @foreach (var item in ViewBag.TeamId)
                                {
                                    <option value="@item.Value">@item.Text</option>
                                }
                            </select>
                            <div class="form-text">Chọn đội bạn muốn đăng ký tham gia giải đấu này.</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="notes" class="form-label">Ghi chú (không bắt buộc)</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Nhập ghi chú hoặc thông tin bổ sung"></textarea>
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a asp-action="Details" asp-route-id="@Model.Id" class="btn btn-secondary">Quay lại</a>
                            <button type="submit" class="btn btn-success">Xác nhận đăng ký đội</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
