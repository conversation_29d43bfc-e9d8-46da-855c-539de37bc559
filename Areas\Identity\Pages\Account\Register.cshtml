﻿@page
@model RegisterModel

@{
    ViewData["Title"] = "Register";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>@ViewData["Title"]</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(to right, #ff9966, #ff5e62);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            color: #333;
        }

        h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
        }

        .form-container {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 8px 16px rgba(0,0,0,0.15);
            max-width: 500px;
            margin: auto;
        }

        .btn-primary {
            background-color: #ff6600;
            border-color: #ff6600;
        }

            .btn-primary:hover {
                background-color: #e65c00;
                border-color: #e65c00;
            }

        a {
            color: #ff6600;
        }

            a:hover {
                color: #e65c00;
            }

        .form-floating > .form-control:focus ~ label {
            color: #ff6600;
        }

        .form-label {
            color: #555;
        }
    </style>
</head>



<h1>@ViewData["Title"]</h1>

<div class="form-container">
    <form id="registerForm" asp-route-returnUrl="@Model.ReturnUrl" method="post">
        <h2 class="text-center mb-4">Tạo tài khoản mới</h2>
        <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>

        <!-- Field for FullName -->
        <div class="form-floating mb-3">
            <input asp-for="Input.FullName" class="form-control" placeholder="Họ và Tên" />
            <label asp-for="Input.FullName">Họ và Tên</label>
            <span asp-validation-for="Input.FullName" class="text-danger"></span>
        </div>


        <div class="form-floating mb-3">
            <input asp-for="Input.Email" class="form-control" placeholder="<EMAIL>" />
            <label asp-for="Input.Email">Email</label>
            <span asp-validation-for="Input.Email" class="text-danger"></span>
        </div>

        <div class="form-floating mb-3">
            <input asp-for="Input.Password" class="form-control" placeholder="password" />
            <label asp-for="Input.Password">Mật khẩu</label>
            <span asp-validation-for="Input.Password" class="text-danger"></span>
        </div>

        <div class="form-floating mb-3">
            <input asp-for="Input.ConfirmPassword" class="form-control" placeholder="confirm password" />
            <label asp-for="Input.ConfirmPassword">Xác nhận mật khẩu</label>
            <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
        </div>

        <!-- Role selection removed - users will be assigned the User role by default -->

        <button id="registerSubmit" type="submit" class="w-100 btn btn-lg btn-primary">Đăng ký</button>
    </form>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
