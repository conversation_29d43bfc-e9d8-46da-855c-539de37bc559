@model WebQuanLyGiaiDau_NhomTD.Models.Tournament

@{
    ViewData["Title"] = "Đăng ký giải đấu";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow">
                <div class="card-header bg-primary text-white">
                    <h2 class="text-center">Đăng ký giải đấu</h2>
                </div>
                <div class="card-body">
                    @if (ViewBag.AlreadyRegistered == true)
                    {
                        <div class="alert alert-info">
                            <h4 class="alert-heading">Bạn đã đăng ký giải đấu này!</h4>
                            <p>Trạng thái đăng ký: <strong>@ViewBag.RegistrationStatus</strong></p>
                            <hr>
                            <p class="mb-0">Vui lòng chờ ban tổ chức xét duyệt đăng ký của bạn.</p>
                        </div>
                        <div class="text-center mt-3">
                            <a asp-action="Index" class="btn btn-secondary">Quay lại danh sách giải đấu</a>
                        </div>
                    }
                    else
                    {
                        <div class="row mb-4">
                            <div class="col-md-4">
                                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                                {
                                    <img src="@Model.ImageUrl" class="img-fluid rounded" alt="@Model.Name">
                                }
                                else
                                {
                                    <div class="bg-light p-4 text-center rounded">
                                        <span class="text-muted">Không có hình ảnh</span>
                                    </div>
                                }
                            </div>
                            <div class="col-md-8">
                                <h3>@Model.Name</h3>
                                <p class="text-muted">@Model.Description</p>
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <strong>Ngày bắt đầu:</strong>
                                        <p>@Model.StartDate.ToString("dd/MM/yyyy")</p>
                                    </div>
                                    <div>
                                        <strong>Ngày kết thúc:</strong>
                                        <p>@Model.EndDate.ToString("dd/MM/yyyy")</p>
                                    </div>
                                </div>
                                <div>
                                    <strong>Môn thể thao:</strong>
                                    <p>@(Model.Sports?.Name ?? "Không xác định")</p>
                                </div>
                            </div>
                        </div>

                        <form asp-action="Register" method="post" enctype="multipart/form-data">
                            <input type="hidden" name="id" value="@Model.Id" />

                            <div class="mb-3">
                                <label for="notes" class="form-label">Ghi chú (không bắt buộc)</label>
                                <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="Nhập ghi chú hoặc thông tin bổ sung"></textarea>
                            </div>

                            <div class="mb-3">
                            <label class="form-label">Đăng ký đội tham gia</label>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="teamOption" id="existingTeam" value="existing" checked>
                                <label class="form-check-label" for="existingTeam">
                                    Sử dụng đội hiện có
                                </label>
                            </div>
                            <div class="form-check mb-2">
                                <input class="form-check-input" type="radio" name="teamOption" id="newTeam" value="new">
                                <label class="form-check-label" for="newTeam">
                                    Tạo đội mới
                                </label>
                            </div>
                        </div>

                        <div id="existingTeamSection" class="mb-3">
                            @if (ViewBag.UserTeams != null && ViewBag.UserTeams.Count > 0)
                            {
                                <label for="teamId" class="form-label">Chọn đội</label>
                                <select name="teamId" id="teamId" class="form-select">
                                    <option value="">-- Chọn đội --</option>
                                    @foreach (var team in ViewBag.UserTeams)
                                    {
                                        <option value="@team.TeamId">@team.Name</option>
                                    }
                                </select>
                                <div class="form-text">Chọn đội bạn muốn đăng ký tham gia giải đấu này.</div>
                            }
                            else
                            {
                                <div class="alert alert-warning">
                                    <i class="bi bi-exclamation-triangle"></i> Bạn chưa có đội nào. Vui lòng tạo đội mới.
                                </div>
                                <script>
                                    document.getElementById('newTeam').checked = true;
                                    document.getElementById('existingTeam').disabled = true;
                                </script>
                            }
                        </div>

                        <div id="newTeamSection" class="mb-3" style="display: none;">
                            <div class="mb-3">
                                <label for="newTeamName" class="form-label">Tên đội mới</label>
                                <input type="text" class="form-control" id="newTeamName" name="newTeamName" placeholder="Nhập tên đội mới">
                            </div>
                            <div class="mb-3">
                                <label for="logoUrl" class="form-label">Logo đội (không bắt buộc)</label>
                                <input type="file" class="form-control" id="logoFile" name="logoFile" accept="image/*">
                                <div class="form-text">Chọn hình ảnh logo cho đội của bạn (JPG, PNG).</div>
                            </div>
                        </div>

                        <script>
                            document.addEventListener('DOMContentLoaded', function() {
                                const existingTeamRadio = document.getElementById('existingTeam');
                                const newTeamRadio = document.getElementById('newTeam');
                                const existingTeamSection = document.getElementById('existingTeamSection');
                                const newTeamSection = document.getElementById('newTeamSection');

                                function toggleSections() {
                                    if (existingTeamRadio.checked) {
                                        existingTeamSection.style.display = 'block';
                                        newTeamSection.style.display = 'none';
                                    } else {
                                        existingTeamSection.style.display = 'none';
                                        newTeamSection.style.display = 'block';
                                    }
                                }

                                existingTeamRadio.addEventListener('change', toggleSections);
                                newTeamRadio.addEventListener('change', toggleSections);

                                // Initial toggle
                                toggleSections();
                            });
                        </script>

                            <div class="d-flex justify-content-between">
                                <a asp-action="Index" class="btn btn-secondary">Quay lại</a>
                                <button type="submit" class="btn btn-success">Xác nhận đăng ký</button>
                            </div>
                        </form>
                    }
                </div>
            </div>
        </div>
    </div>
</div>
