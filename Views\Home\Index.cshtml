@{
    ViewData["Title"] = "Trang chủ - Thể Thao 24/7";
}

<!-- Enhanced 24/7 Sports Hero Section -->
<div class="sports-hero-enhanced sports-24-7" id="hero-section">
    <!-- Live Sports Indicator -->
    <div class="live-indicator position-absolute" style="top: 20px; right: 20px; z-index: 10;">
        <div class="pulse-dot"></div>
        <span>LIVE 24/7</span>
    </div>

    <!-- Animated Background Elements -->
    <div class="hero-particles"></div>
    <div class="hero-waves"></div>

    <div class="container">
        <div class="row align-items-center min-vh-100">
            <div class="col-lg-6 text-center text-lg-start hero-content">
                <div class="hero-badge animate-bounce-in energy-button" data-delay="0.2">
                    <i class="bi bi-trophy-fill me-2"></i>
                    Nền tảng Th<PERSON>hao #1 Việt Nam
                </div>

                <h1 class="hero-title animate-slide-up dynamic-entrance" data-delay="0.4">
                    <span class="text-gradient sports-wave">Th<PERSON> 24/7</span>
                    <span class="typing-text" data-text="Giải Đấu Không Giới Hạn"></span>
                </h1>

                <p class="hero-subtitle animate-slide-up" data-delay="0.6">
                    🏆 Nền tảng quản lý giải đấu thể thao năng động nhất Việt Nam!
                    ⚡ Tham gia ngay để trải nghiệm thể thao đỉnh cao 24/7 cùng hàng nghìn vận động viên chuyên nghiệp.
                </p>

                <div class="hero-stats animate-slide-up" data-delay="0.8">
                    <div class="stat-item champion-card">
                        <div class="stat-number stats-animated" data-count="500">0</div>
                        <div class="stat-label">🏆 Giải đấu LIVE</div>
                    </div>
                    <div class="stat-item champion-card">
                        <div class="stat-number stats-animated" data-count="1200">0</div>
                        <div class="stat-label">⚽ Đội thi đấu</div>
                    </div>
                    <div class="stat-item champion-card">
                        <div class="stat-number stats-animated" data-count="8500">0</div>
                        <div class="stat-label">🥇 Vận động viên</div>
                    </div>
                </div>

                @if (User.Identity == null || !User.Identity.IsAuthenticated)
                {
                    <div class="hero-buttons animate-slide-up" data-delay="1.0">
                        <a asp-area="Identity" asp-page="/Account/Login" class="btn btn-sports-primary btn-sports-xl hero-btn-primary energy-button">
                            <i class="bi bi-rocket-takeoff"></i>
                            <span>🚀 Tham Gia Ngay</span>
                            <div class="btn-shine"></div>
                        </a>
                        <a asp-area="Identity" asp-page="/Account/Register" class="btn btn-sports-outline btn-sports-xl hero-btn-secondary energy-button">
                            <i class="bi bi-star-fill"></i>
                            <span>⭐ Đăng Ký VIP</span>
                        </a>
                    </div>
                }
                else
                {
                    <div class="hero-buttons animate-slide-up" data-delay="1.0">
                        <a asp-controller="Sports" asp-action="Index" class="btn btn-sports-primary btn-sports-xl hero-btn-primary energy-button">
                            <i class="bi bi-fire"></i>
                            <span>🔥 Khám Phá Thể Thao</span>
                            <div class="btn-shine"></div>
                        </a>
                        <a asp-controller="Tournament" asp-action="Index" class="btn btn-sports-outline btn-sports-xl hero-btn-secondary energy-button">
                            <i class="bi bi-trophy-fill"></i>
                            <span>🏆 Giải Đấu HOT</span>
                        </a>
                    </div>
                }
            </div>

            <div class="col-lg-6 d-none d-lg-block hero-visual">
                <div class="logo-container animate-float-3d" data-delay="0.5">
                    <div class="logo-glow"></div>
                    <img src="/images/Logomain.png" alt="Logo" class="img-fluid main-logo" loading="lazy">
                    <div class="logo-particles"></div>
                </div>
            </div>
        </div>

        <!-- Scroll Indicator -->
        <div class="scroll-indicator animate-bounce">
            <div class="scroll-arrow">
                <i class="bi bi-chevron-down"></i>
            </div>
            <span>Khám phá thêm</span>
        </div>
    </div>
</div>

<!-- How It Works Section -->
<section class="how-it-works-section py-5" id="how-it-works">
    <div class="container">
        <div class="text-center mb-5 section-header">
            <div class="section-badge animate-fade-in-up">
                <i class="bi bi-lightbulb me-2"></i>Quy trình đơn giản
            </div>
            <h2 class="animate-fade-in-up" data-delay="0.1">Cách Thức Hoạt Động</h2>
            <p class="text-muted animate-fade-in-up" data-delay="0.2">
                Chỉ với 4 bước đơn giản, bạn có thể tham gia và quản lý giải đấu một cách hiệu quả
            </p>
        </div>

        <div class="row g-4">
            <div class="col-lg-3 col-md-6 step-item" data-step="1">
                <div class="step-card animate-scale-in" data-delay="0.1">
                    <div class="step-number">01</div>
                    <div class="step-icon">
                        <i class="bi bi-person-plus-fill"></i>
                    </div>
                    <h4>Đăng ký tài khoản</h4>
                    <p>Tạo tài khoản miễn phí và hoàn thiện thông tin cá nhân</p>
                    <div class="step-progress"></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 step-item" data-step="2">
                <div class="step-card animate-scale-in" data-delay="0.2">
                    <div class="step-number">02</div>
                    <div class="step-icon">
                        <i class="bi bi-people-fill"></i>
                    </div>
                    <h4>Tạo đội bóng</h4>
                    <p>Thành lập đội bóng và mời các thành viên tham gia</p>
                    <div class="step-progress"></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 step-item" data-step="3">
                <div class="step-card animate-scale-in" data-delay="0.3">
                    <div class="step-number">03</div>
                    <div class="step-icon">
                        <i class="bi bi-trophy-fill"></i>
                    </div>
                    <h4>Đăng ký giải đấu</h4>
                    <p>Chọn giải đấu phù hợp và đăng ký tham gia</p>
                    <div class="step-progress"></div>
                </div>
            </div>

            <div class="col-lg-3 col-md-6 step-item" data-step="4">
                <div class="step-card animate-scale-in" data-delay="0.4">
                    <div class="step-number">04</div>
                    <div class="step-icon">
                        <i class="bi bi-play-circle-fill"></i>
                    </div>
                    <h4>Thi đấu & Thắng</h4>
                    <p>Tham gia thi đấu và theo dõi kết quả trực tiếp</p>
                    <div class="step-progress"></div>
                </div>
            </div>
        </div>

        <!-- Process Flow Animation -->
        <div class="process-flow animate-fade-in-up" data-delay="0.5">
            <svg class="flow-line" viewBox="0 0 1200 100">
                <path class="flow-path" d="M50,50 Q300,10 550,50 T1050,50" stroke-dasharray="10,5"/>
            </svg>
        </div>
    </div>
</section>

<!-- Enhanced 24/7 Sports Features Section -->
<section class="features-section py-5 sports-24-7" id="features">
    <div class="container">
        <div class="text-center mb-5 section-header">
            <div class="section-badge animate-fade-in-up energy-button">
                <i class="bi bi-lightning-charge me-2"></i>⚡ Tính năng siêu việt 24/7
            </div>
            <h2 class="animate-fade-in-up dynamic-entrance" data-delay="0.1">🚀 Công Nghệ Thể Thao Đỉnh Cao</h2>
            <p class="text-muted animate-fade-in-up" data-delay="0.2">
                💪 Trải nghiệm những tính năng cách mạng giúp quản lý giải đấu thể thao chuyên nghiệp như các giải đấu quốc tế!
            </p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6 feature-item">
                <div class="sports-card tournament-card-enhanced champion-card h-100 hover-3d" data-tilt>
                    <div class="card-body">
                        <div class="card-icon feature-icon-1 energy-button">
                            <i class="bi bi-trophy-fill"></i>
                            <div class="icon-bg"></div>
                        </div>
                        <h3 class="card-title">🏆 Giải Đấu LIVE 24/7</h3>
                        <p class="card-text">⚡ Tạo và quản lý giải đấu thể thao LIVE với AI thông minh, lập lịch tự động và phát sóng trực tiếp!</p>
                        <div class="feature-list">
                            <div class="feature-item-small">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span>🤖 AI Lập lịch thông minh</span>
                            </div>
                            <div class="feature-item-small">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span>📺 Phát sóng LIVE</span>
                            </div>
                        </div>
                        <div class="mt-auto">
                            <a href="#" class="btn btn-sports-outline btn-sports-sm feature-btn energy-button">
                                <span>🔥 Khám phá ngay</span>
                                <i class="bi bi-rocket-takeoff"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 feature-item">
                <div class="sports-card tournament-card-enhanced champion-card h-100 hover-3d" data-tilt>
                    <div class="card-body">
                        <div class="card-icon feature-icon-2 energy-button">
                            <i class="bi bi-people-fill"></i>
                            <div class="icon-bg"></div>
                        </div>
                        <h3 class="card-title">⚽ Đội Hình Siêu Sao</h3>
                        <p class="card-text">🌟 Quản lý đội bóng như HLV chuyên nghiệp! Theo dõi stats, phong độ và thành tích của từng cầu thủ real-time!</p>
                        <div class="feature-list">
                            <div class="feature-item-small">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span>📊 Analytics chuyên sâu</span>
                            </div>
                            <div class="feature-item-small">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span>🏅 Ranking thời gian thực</span>
                            </div>
                        </div>
                        <div class="mt-auto">
                            <a href="#" class="btn btn-sports-outline btn-sports-sm feature-btn energy-button">
                                <span>⭐ Xem ngay</span>
                                <i class="bi bi-star-fill"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 feature-item">
                <div class="sports-card tournament-card-enhanced champion-card h-100 hover-3d" data-tilt>
                    <div class="card-body">
                        <div class="card-icon feature-icon-3 energy-button">
                            <i class="bi bi-lightning-charge-fill"></i>
                            <div class="icon-bg"></div>
                        </div>
                        <h3 class="card-title">⚡ Đăng Ký Tốc Độ Ánh Sáng</h3>
                        <p class="card-text">🚀 Đăng ký giải đấu chỉ trong 30 giây! Hệ thống AI tự động match đối thủ phù hợp và thông báo real-time!</p>
                        <div class="feature-list">
                            <div class="feature-item-small">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span>⚡ Đăng ký siêu tốc</span>
                            </div>
                            <div class="feature-item-small">
                                <i class="bi bi-check-circle-fill text-success"></i>
                                <span>🔔 Thông báo LIVE</span>
                            </div>
                        </div>
                        <div class="mt-auto">
                            <a href="#" class="btn btn-sports-outline btn-sports-sm feature-btn energy-button">
                                <span>💥 Thử ngay</span>
                                <i class="bi bi-rocket-takeoff"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Tin tức thể thao -->
<section class="news-section py-5 bg-light">
    <div class="container">
        <div class="text-center mb-5" data-animation="animate-fade-in-up">
            <h2 class="mb-3">Tin Tức Thể Thao</h2>
            <p class="text-muted">Cập nhật những tin tức mới nhất về thể thao</p>
        </div>

        <!-- Tin tức nổi bật -->
        <div class="featured-news mb-5" data-animation="animate-fade-in-up" data-delay="0.2">
            <div class="row" id="featuredNewsContainer">
                <!-- Dữ liệu sẽ được tải bằng AJAX -->
                <div class="col-12 text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Đang tải tin tức...</p>
                </div>
            </div>
        </div>

        <!-- Tin tức mới nhất -->
        <div data-animation="animate-fade-in-up" data-delay="0.3">
            <h3 class="mb-4">Tin tức mới nhất</h3>
            <div class="row g-4" id="latestNewsContainer">
                <!-- Dữ liệu sẽ được tải bằng AJAX -->
                <div class="col-12 text-center">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Đang tải tin tức...</p>
                </div>
            </div>
        </div>

        <div class="text-center mt-5" data-animation="animate-fade-in-up" data-delay="0.4">
            <a asp-controller="News" asp-action="Index" class="btn btn-sports-primary btn-sports-lg">
                <i class="bi bi-newspaper"></i>Xem tất cả tin tức
            </a>
        </div>
    </div>
</section>

<!-- Featured Tournaments Carousel -->
<section class="featured-tournaments-section py-5" id="tournaments">
    <div class="container">
        <div class="text-center mb-5 section-header">
            <div class="section-badge animate-fade-in-up">
                <i class="bi bi-trophy me-2"></i>Giải đấu nổi bật
            </div>
            <h2 class="animate-fade-in-up" data-delay="0.1">Giải Đấu Đang Diễn Ra</h2>
            <p class="text-muted animate-fade-in-up" data-delay="0.2">
                Tham gia các giải đấu hấp dẫn đang được tổ chức trên toàn quốc
            </p>
        </div>

        <div class="tournament-carousel-container">
            <div class="tournament-carousel" id="tournamentCarousel">
                <!-- Tournament cards will be loaded here via JavaScript -->
                <div class="tournament-loading">
                    <div class="loading-spinner"></div>
                    <p class="mt-3 text-muted">Đang tải giải đấu...</p>
                </div>
            </div>

            <!-- Carousel Controls -->
            <div class="carousel-controls">
                <button class="carousel-btn prev-btn" id="prevTournament">
                    <i class="bi bi-chevron-left"></i>
                </button>
                <button class="carousel-btn next-btn" id="nextTournament">
                    <i class="bi bi-chevron-right"></i>
                </button>
            </div>
        </div>

        <div class="text-center mt-4">
            <a asp-controller="Tournament" asp-action="Index" class="btn btn-sports-outline btn-sports-lg">
                <i class="bi bi-trophy me-2"></i>Xem tất cả giải đấu
            </a>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="testimonials-section py-5 bg-light" id="testimonials">
    <div class="container">
        <div class="text-center mb-5 section-header">
            <div class="section-badge animate-fade-in-up">
                <i class="bi bi-chat-heart me-2"></i>Phản hồi từ người dùng
            </div>
            <h2 class="animate-fade-in-up" data-delay="0.1">Khách Hàng Nói Gì Về Chúng Tôi</h2>
            <p class="text-muted animate-fade-in-up" data-delay="0.2">
                Hàng nghìn đội bóng và cầu thủ đã tin tưởng sử dụng hệ thống của chúng tôi
            </p>
        </div>

        <div class="row g-4">
            <div class="col-lg-4 col-md-6 testimonial-item">
                <div class="testimonial-card animate-fade-in-up" data-delay="0.1">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <p class="testimonial-text">
                            "Hệ thống quản lý giải đấu tuyệt vời! Giúp đội bóng của chúng tôi tổ chức các trận đấu một cách chuyên nghiệp và hiệu quả."
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/images/testimonials/avatar1.jpg" alt="Nguyễn Văn A" loading="lazy">
                        </div>
                        <div class="author-info">
                            <h5>Nguyễn Văn A</h5>
                            <span>Đội trưởng FC Hà Nội</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 testimonial-item">
                <div class="testimonial-card animate-fade-in-up" data-delay="0.2">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <p class="testimonial-text">
                            "Giao diện thân thiện, dễ sử dụng. Tính năng thống kê chi tiết giúp chúng tôi theo dõi phong độ cầu thủ rất tốt."
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/images/testimonials/avatar2.jpg" alt="Trần Thị B" loading="lazy">
                        </div>
                        <div class="author-info">
                            <h5>Trần Thị B</h5>
                            <span>Huấn luyện viên CLB Sài Gòn</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-4 col-md-6 testimonial-item">
                <div class="testimonial-card animate-fade-in-up" data-delay="0.3">
                    <div class="testimonial-content">
                        <div class="stars">
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                            <i class="bi bi-star-fill"></i>
                        </div>
                        <p class="testimonial-text">
                            "Hỗ trợ khách hàng tận tình, phản hồi nhanh chóng. Hệ thống ổn định, ít khi gặp lỗi trong quá trình sử dụng."
                        </p>
                    </div>
                    <div class="testimonial-author">
                        <div class="author-avatar">
                            <img src="/images/testimonials/avatar3.jpg" alt="Lê Văn C" loading="lazy">
                        </div>
                        <div class="author-info">
                            <h5>Lê Văn C</h5>
                            <span>Quản lý giải đấu Đà Nẵng</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Testimonial Stats -->
        <div class="testimonial-stats mt-5">
            <div class="row text-center">
                <div class="col-md-3 col-6">
                    <div class="stat-item animate-counter" data-count="4.9">0</div>
                    <div class="stat-label">Đánh giá trung bình</div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item animate-counter" data-count="1200">0</div>
                    <div class="stat-label">Khách hàng hài lòng</div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item animate-counter" data-count="98">0</div>
                    <div class="stat-label">% Tỷ lệ hài lòng</div>
                </div>
                <div class="col-md-3 col-6">
                    <div class="stat-item animate-counter" data-count="24">0</div>
                    <div class="stat-label">Hỗ trợ 24/7</div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Call to Action Section -->
<section class="cta-section py-5" id="cta">
    <div class="container">
        <div class="cta-content text-center">
            <div class="cta-icon animate-bounce-in">
                <i class="bi bi-rocket-takeoff"></i>
            </div>
            <h2 class="animate-fade-in-up" data-delay="0.1">Sẵn Sàng Bắt Đầu?</h2>
            <p class="animate-fade-in-up" data-delay="0.2">
                Tham gia cùng hàng nghìn đội bóng đã tin tưởng sử dụng hệ thống quản lý giải đấu của chúng tôi
            </p>

            <div class="cta-features animate-fade-in-up" data-delay="0.3">
                <div class="cta-feature">
                    <i class="bi bi-check-circle-fill"></i>
                    <span>Miễn phí 30 ngày đầu</span>
                </div>
                <div class="cta-feature">
                    <i class="bi bi-check-circle-fill"></i>
                    <span>Không cần thẻ tín dụng</span>
                </div>
                <div class="cta-feature">
                    <i class="bi bi-check-circle-fill"></i>
                    <span>Hỗ trợ 24/7</span>
                </div>
            </div>

            @if (User.Identity == null || !User.Identity.IsAuthenticated)
            {
                <div class="cta-buttons animate-fade-in-up" data-delay="0.4">
                    <a asp-area="Identity" asp-page="/Account/Register" class="btn btn-sports-primary btn-sports-xl cta-btn-primary">
                        <i class="bi bi-person-plus"></i>
                        <span>Đăng ký miễn phí ngay</span>
                        <div class="btn-shine"></div>
                    </a>
                    <a asp-controller="Tournament" asp-action="Index" class="btn btn-sports-outline btn-sports-xl">
                        <i class="bi bi-eye"></i>
                        <span>Xem demo</span>
                    </a>
                </div>
            }
            else
            {
                <div class="cta-buttons animate-fade-in-up" data-delay="0.4">
                    <a asp-controller="Tournament" asp-action="Create" class="btn btn-sports-primary btn-sports-xl cta-btn-primary">
                        <i class="bi bi-plus-circle"></i>
                        <span>Tạo giải đấu đầu tiên</span>
                        <div class="btn-shine"></div>
                    </a>
                    <a asp-controller="Sports" asp-action="Index" class="btn btn-sports-outline btn-sports-xl">
                        <i class="bi bi-dribbble"></i>
                        <span>Khám phá môn thể thao</span>
                    </a>
                </div>
            }
        </div>
    </div>
</section>

<section class="sports-section py-5">
    <div class="container">
        <div class="text-center mb-5" data-animation="animate-fade-in-up">
            <h2 class="mb-3">Các Môn Thể Thao</h2>
            <p class="text-muted">Hệ thống hỗ trợ đa dạng các môn thể thao phổ biến</p>
        </div>

        <div class="row g-4 justify-content-center">
            <div class="col-lg-3 col-md-4 col-6 text-center" data-animation="animate-scale-in" data-delay="0.1">
                <div class="sport-icon-container hover-lift">
                    <div class="sport-icon-bg">
                        <img src="/images/basketball-icon.png" alt="Bóng rổ" class="img-fluid sport-icon">
                    </div>
                    <h4 class="mt-3">Bóng rổ</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-6 text-center" data-animation="animate-scale-in" data-delay="0.2">
                <div class="sport-icon-container hover-lift">
                    <div class="sport-icon-bg">
                        <img src="/images/football_icon.jpeg" alt="Bóng đá" class="img-fluid sport-icon">
                    </div>
                    <h4 class="mt-3">Bóng đá</h4>
                </div>
            </div>
            <div class="col-lg-3 col-md-4 col-6 text-center" data-animation="animate-scale-in" data-delay="0.3">
                <div class="sport-icon-container hover-lift">
                    <div class="sport-icon-bg">
                        <i class="bi bi-dribbble sport-icon-fallback"></i>
                    </div>
                    <h4 class="mt-3">Các môn khác</h4>
                </div>
            </div>
        </div>
    </div>
</section>



<!-- Enhanced JavaScript for Homepage Interactions -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize all homepage features
        initializeHomepageAnimations();
        initializeParallaxEffects();
        initializeCounterAnimations();
        initializeTypingEffect();
        initializeTournamentCarousel();
        initializeTestimonialAnimations();
        initializeSmoothScrolling();
        loadNewsContent();

        // Initialize 3D tilt effects
        if (typeof VanillaTilt !== 'undefined') {
            VanillaTilt.init(document.querySelectorAll("[data-tilt]"), {
                max: 15,
                speed: 400,
                glare: true,
                "max-glare": 0.2,
            });
        }
    });

    // Enhanced Animation System
    function initializeHomepageAnimations() {
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const element = entry.target;
                    const delay = element.dataset.delay || 0;

                    setTimeout(() => {
                        if (element.classList.contains('animate-slide-up')) {
                            element.style.transform = 'translateY(0)';
                            element.style.opacity = '1';
                        } else if (element.classList.contains('animate-bounce-in')) {
                            element.style.transform = 'scale(1)';
                            element.style.opacity = '1';
                        } else if (element.classList.contains('animate-scale-in')) {
                            element.style.transform = 'scale(1)';
                            element.style.opacity = '1';
                        } else if (element.classList.contains('animate-fade-in-up')) {
                            element.style.transform = 'translateY(0)';
                            element.style.opacity = '1';
                        }

                        element.classList.add('animated');
                    }, delay * 1000);

                    observer.unobserve(element);
                }
            });
        }, observerOptions);

        // Observe all animated elements
        document.querySelectorAll('.animate-slide-up, .animate-bounce-in, .animate-scale-in, .animate-fade-in-up').forEach(el => {
            observer.observe(el);
        });
    }

    // Parallax Effects
    function initializeParallaxEffects() {
        const heroSection = document.getElementById('hero-section');
        const particles = document.querySelector('.hero-particles');
        const waves = document.querySelector('.hero-waves');

        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const rate = scrolled * -0.5;
            const rate2 = scrolled * -0.3;

            if (particles) {
                particles.style.transform = `translateY(${rate}px)`;
            }
            if (waves) {
                waves.style.transform = `translateY(${rate2}px)`;
            }
        });
    }

    // Animated Counters
    function initializeCounterAnimations() {
        const counters = document.querySelectorAll('.stat-number[data-count], .animate-counter[data-count]');

        const counterObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const counter = entry.target;
                    const target = parseInt(counter.dataset.count);
                    const duration = 2000;
                    const step = target / (duration / 16);
                    let current = 0;

                    const updateCounter = () => {
                        current += step;
                        if (current < target) {
                            counter.textContent = Math.floor(current);
                            requestAnimationFrame(updateCounter);
                        } else {
                            counter.textContent = target;
                        }
                    };

                    updateCounter();
                    counterObserver.unobserve(counter);
                }
            });
        });

        counters.forEach(counter => counterObserver.observe(counter));
    }

    // Typing Effect
    function initializeTypingEffect() {
        const typingElement = document.querySelector('.typing-text');
        if (!typingElement) return;

        const text = typingElement.dataset.text;
        let index = 0;

        function typeText() {
            if (index < text.length) {
                typingElement.textContent += text.charAt(index);
                index++;
                setTimeout(typeText, 100);
            }
        }

        setTimeout(() => {
            typingElement.textContent = '';
            typeText();
        }, 1000);
    }

    // Tournament Carousel
    function initializeTournamentCarousel() {
        // Load featured tournaments (mock data for now)
        const tournamentCarousel = document.getElementById('tournamentCarousel');

        // Mock tournament data
        const mockTournaments = [
            {
                id: 1,
                name: "Giải Bóng Rổ Hà Nội Open 2024",
                status: "Đang diễn ra",
                teams: 16,
                image: "/images/tournaments/tournament1.jpg"
            },
            {
                id: 2,
                name: "Cup Bóng Đá Sinh Viên TP.HCM",
                status: "Sắp diễn ra",
                teams: 24,
                image: "/images/tournaments/tournament2.jpg"
            },
            {
                id: 3,
                name: "Giải Bóng Rổ 3x3 Đà Nẵng",
                status: "Đang đăng ký",
                teams: 12,
                image: "/images/tournaments/tournament3.jpg"
            }
        ];

        let tournamentHtml = '';
        mockTournaments.forEach((tournament, index) => {
            tournamentHtml += `
                <div class="tournament-card" data-delay="0.${index + 1}">
                    <div class="tournament-image">
                        <img src="${tournament.image}" alt="${tournament.name}" loading="lazy" onerror="this.src='/images/tournaments/default.jpg'">
                        <div class="tournament-status">${tournament.status}</div>
                    </div>
                    <div class="tournament-content">
                        <h4>${tournament.name}</h4>
                        <div class="tournament-info">
                            <span><i class="bi bi-people"></i> ${tournament.teams} đội</span>
                        </div>
                        <a href="/Tournament/Details/${tournament.id}" class="btn btn-sports-primary btn-sports-sm">
                            Xem chi tiết
                        </a>
                    </div>
                </div>
            `;
        });

        tournamentCarousel.innerHTML = tournamentHtml;

        // Carousel controls
        let currentSlide = 0;
        const slides = document.querySelectorAll('.tournament-card');
        const totalSlides = slides.length;

        document.getElementById('nextTournament').addEventListener('click', () => {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateCarousel();
        });

        document.getElementById('prevTournament').addEventListener('click', () => {
            currentSlide = (currentSlide - 1 + totalSlides) % totalSlides;
            updateCarousel();
        });

        function updateCarousel() {
            const offset = -currentSlide * 100;
            tournamentCarousel.style.transform = `translateX(${offset}%)`;
        }
    }

    // Testimonial Animations
    function initializeTestimonialAnimations() {
        const testimonialCards = document.querySelectorAll('.testimonial-card');

        testimonialCards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
            });
        });
    }

    // Smooth Scrolling Navigation
    function initializeSmoothScrolling() {
        // Add navigation dots
        const sections = ['hero-section', 'how-it-works', 'features', 'tournaments', 'testimonials', 'cta'];
        const navDots = document.createElement('div');
        navDots.className = 'scroll-nav-dots';

        sections.forEach((section, index) => {
            const dot = document.createElement('div');
            dot.className = 'nav-dot';
            dot.dataset.section = section;
            dot.addEventListener('click', () => {
                document.getElementById(section).scrollIntoView({
                    behavior: 'smooth'
                });
            });
            navDots.appendChild(dot);
        });

        document.body.appendChild(navDots);

        // Update active dot on scroll
        window.addEventListener('scroll', () => {
            const scrollPosition = window.scrollY + 100;

            sections.forEach((section, index) => {
                const element = document.getElementById(section);
                if (element) {
                    const offsetTop = element.offsetTop;
                    const height = element.offsetHeight;

                    if (scrollPosition >= offsetTop && scrollPosition < offsetTop + height) {
                        document.querySelectorAll('.nav-dot').forEach(dot => dot.classList.remove('active'));
                        document.querySelector(`[data-section="${section}"]`).classList.add('active');
                    }
                }
            });
        });

        // Scroll indicator click
        const scrollIndicator = document.querySelector('.scroll-indicator');
        if (scrollIndicator) {
            scrollIndicator.addEventListener('click', () => {
                document.getElementById('how-it-works').scrollIntoView({
                    behavior: 'smooth'
                });
            });
        }
    }

    // Load News Content (existing functionality)
    function loadNewsContent() {
        // Tải tin tức nổi bật
        fetch('/News/GetFeaturedNews')
            .then(response => response.json())
            .then(data => {
                const featuredNewsContainer = document.getElementById('featuredNewsContainer');
                featuredNewsContainer.innerHTML = '';

                if (data.length > 0) {
                    const featuredHtml = `
                        <div class="col-lg-6 mb-4" data-animation="animate-fade-in-left">
                            <div class="sports-card sports-card-featured featured-news-card">
                                <img src="${data[0].imageUrl || '/images/news/default.jpg'}" alt="${data[0].title}" class="card-img-top" loading="lazy">
                                <div class="card-body">
                                    <div class="card-badge">
                                        <i class="bi bi-star-fill me-1"></i>Nổi bật
                                    </div>
                                    <div class="news-date mb-2">
                                        <i class="bi bi-calendar-event me-1"></i>${data[0].publishDate}
                                    </div>
                                    <h3 class="card-title">${data[0].title}</h3>
                                    <p class="card-text">${data[0].summary}</p>
                                    <a href="/News/Details/${data[0].newsId}" class="btn btn-sports-outline btn-sports-sm">Đọc tiếp</a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6">
                            <div class="row">`;

                    let otherFeaturedHtml = '';
                    for (let i = 1; i < data.length && i < 3; i++) {
                        otherFeaturedHtml += `
                            <div class="col-md-6 mb-4" data-animation="animate-fade-in-right" data-delay="0.${i}">
                                <div class="sports-card news-card">
                                    <img src="${data[i].imageUrl || '/images/news/default.jpg'}" alt="${data[i].title}" class="card-img-top" loading="lazy">
                                    <div class="card-body">
                                        <div class="news-date mb-2">
                                            <i class="bi bi-calendar-event me-1"></i>${data[i].publishDate}
                                        </div>
                                        <h5 class="card-title">${data[i].title}</h5>
                                        <a href="/News/Details/${data[i].newsId}" class="btn btn-sports-outline btn-sports-sm">Đọc tiếp</a>
                                    </div>
                                </div>
                            </div>`;
                    }

                    featuredNewsContainer.innerHTML = featuredHtml + otherFeaturedHtml + '</div></div>';
                } else {
                    featuredNewsContainer.innerHTML = '<div class="col-12 text-center"><p class="text-muted">Không có tin tức nổi bật.</p></div>';
                }
            })
            .catch(error => {
                console.error('Lỗi khi tải tin tức nổi bật:', error);
                document.getElementById('featuredNewsContainer').innerHTML =
                    '<div class="col-12 text-center"><p class="text-danger">Đã xảy ra lỗi khi tải tin tức nổi bật.</p></div>';
            });

        // Tải tin tức mới nhất
        fetch('/News/GetLatestNews')
            .then(response => response.json())
            .then(data => {
                const latestNewsContainer = document.getElementById('latestNewsContainer');
                latestNewsContainer.innerHTML = '';

                if (data.length > 0) {
                    let latestHtml = '';
                    data.forEach((news, index) => {
                        latestHtml += `
                            <div class="col-lg-4 col-md-6 mb-4" data-animation="animate-fade-in-up" data-delay="0.${index + 1}">
                                <div class="sports-card news-card">
                                    <img src="${news.imageUrl || '/images/news/default.jpg'}" alt="${news.title}" class="card-img-top" loading="lazy">
                                    <div class="card-body">
                                        <div class="news-date mb-2">
                                            <i class="bi bi-calendar-event me-1"></i>${news.publishDate}
                                            <span class="ms-2"><i class="bi bi-eye me-1"></i>${news.viewCount}</span>
                                        </div>
                                        <h5 class="card-title">${news.title}</h5>
                                        <p class="card-text">${news.summary}</p>
                                        <a href="/News/Details/${news.newsId}" class="btn btn-sports-outline btn-sports-sm">Đọc tiếp</a>
                                    </div>
                                </div>
                            </div>`;
                    });
                    latestNewsContainer.innerHTML = latestHtml;
                } else {
                    latestNewsContainer.innerHTML = '<div class="col-12 text-center"><p class="text-muted">Không có tin tức mới.</p></div>';
                }
            })
            .catch(error => {
                console.error('Lỗi khi tải tin tức mới nhất:', error);
                document.getElementById('latestNewsContainer').innerHTML =
                    '<div class="col-12 text-center"><p class="text-danger">Đã xảy ra lỗi khi tải tin tức mới nhất.</p></div>';
            });
    }
</script>
