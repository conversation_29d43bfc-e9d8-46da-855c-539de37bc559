@model WebQuanLyGiaiDau_NhomTD.Models.TournamentSubmission

@{
    ViewData["Title"] = "Chỉnh sửa bài viết";
}

<div class="container mt-4">
    <h1 class="text-center text-primary mb-4">Chỉnh sửa bài viết</h1>

    <div class="mb-3">
        <a asp-action="ManageSubmissions" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Quay lại danh sách bài viết
        </a>
    </div>

    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h3 class="card-title mb-0">Thông tin bài viết</h3>
        </div>
        <div class="card-body">
            <form asp-action="EditSubmission" method="post" enctype="multipart/form-data">
                <input type="hidden" asp-for="Id" />
                <input type="hidden" asp-for="UserId" />
                <input type="hidden" asp-for="TournamentId" />
                <input type="hidden" asp-for="SubmissionDate" />

                <div class="row mb-3">
                    <div class="col-md-6">
                        <label class="form-label">Người nộp</label>
                        <input type="text" class="form-control" value="@Model.User.FullName" readonly />
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Giải đấu</label>
                        <input type="text" class="form-control" value="@Model.Tournament.Name" readonly />
                    </div>
                </div>

                <div class="mb-3">
                    <label asp-for="Title" class="form-label">Tiêu đề</label>
                    <input asp-for="Title" class="form-control" required maxlength="200" />
                    <span asp-validation-for="Title" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="Content" class="form-label">Nội dung</label>
                    <textarea asp-for="Content" class="form-control" rows="10" required></textarea>
                    <span asp-validation-for="Content" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label class="form-label">Tài liệu đính kèm hiện tại</label>
                    @if (!string.IsNullOrEmpty(Model.FileUrl))
                    {
                        <div>
                            <a href="@Model.FileUrl" target="_blank" class="btn btn-outline-primary">
                                <i class="bi bi-file-earmark"></i> @Model.FileName
                            </a>
                        </div>
                    }
                    else
                    {
                        <p class="text-muted">Không có tài liệu đính kèm</p>
                    }
                </div>

                <div class="mb-3">
                    <label for="documentFile" class="form-label">Thay đổi tài liệu đính kèm (tùy chọn)</label>
                    <input type="file" id="documentFile" name="documentFile" class="form-control" accept=".doc,.docx,.pdf">
                    <div class="form-text">Chấp nhận các định dạng: .doc, .docx, .pdf. Để trống nếu không muốn thay đổi.</div>
                </div>

                <div class="mb-3">
                    <label asp-for="Status" class="form-label">Trạng thái</label>
                    <select asp-for="Status" class="form-select" required>
                        <option value="Pending">Đang chờ duyệt</option>
                        <option value="Approved">Đã duyệt</option>
                        <option value="Rejected">Từ chối</option>
                    </select>
                    <span asp-validation-for="Status" class="text-danger"></span>
                </div>

                <div class="mb-3">
                    <label asp-for="AdminNotes" class="form-label">Ghi chú của admin</label>
                    <textarea asp-for="AdminNotes" class="form-control" rows="3"></textarea>
                    <span asp-validation-for="AdminNotes" class="text-danger"></span>
                </div>

                <div class="d-flex justify-content-between">
                    <a asp-action="ManageSubmissions" class="btn btn-secondary">
                        <i class="bi bi-x-circle"></i> Hủy
                    </a>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-save"></i> Lưu thay đổi
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
