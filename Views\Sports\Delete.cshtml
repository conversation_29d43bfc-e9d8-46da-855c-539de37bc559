@model WebQuanLyGiaiDau_NhomTD.Models.Sports

@{
    ViewData["Title"] = "Xóa Môn Thể Thao";
}

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 offset-md-2">
            <div class="card shadow border-danger">
                <div class="card-header bg-danger text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-exclamation-triangle"></i> Xác Nhận Xóa
                    </h4>
                </div>
                <div class="card-body">
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger">
                            <i class="bi bi-exclamation-circle"></i> @TempData["ErrorMessage"]
                        </div>
                    }

                    @if (TempData["WarningMessage"] != null)
                    {
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i> @TempData["WarningMessage"]
                        </div>
                    }

                    <div class="alert alert-warning">
                        <h5>Bạn có chắc chắn muốn xóa môn thể thao này?</h5>
                        <p class="mb-0">Hành động này không thể hoàn tác và có thể ảnh hưởng đến các giải đấu liên quan.</p>
                    </div>

                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Thông Tin Môn Thể Thao</h5>
                            <dl class="row">
                                <dt class="col-sm-4">Tên:</dt>
                                <dd class="col-sm-8">@Model.Name</dd>
                            </dl>
                        </div>
                        <div class="col-md-6 text-center">
                            @if (!string.IsNullOrEmpty(Model.ImageUrl))
                            {
                                <img src="@Model.ImageUrl" alt="@Model.Name" class="img-thumbnail" style="max-height: 150px;" />
                            }
                            else
                            {
                                <div class="bg-light p-4 rounded text-muted">
                                    <i class="bi bi-image display-4"></i>
                                    <p>Không có hình ảnh</p>
                                </div>
                            }
                        </div>
                    </div>

                    <form asp-action="Delete" method="post">
                        <input type="hidden" asp-for="Id" />
                        <div class="d-flex justify-content-between">
                            <a asp-action="Index" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left"></i> Quay Lại
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="bi bi-trash"></i> Xác Nhận Xóa
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
