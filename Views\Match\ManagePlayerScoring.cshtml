@model WebQuanLyGiaiDau_NhomTD.Models.ViewModels.PlayerScoringViewModel

@{
    ViewData["Title"] = "Quản Lý Điểm Số Cầu Thủ";
}

<h1 class="text-center text-primary mt-4">📊 Quản Lý Điểm Số Cầu Thủ</h1>

<div class="container bg-light rounded shadow p-4 mt-4 mb-4">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">Thông Tin Trận Đấu</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Giải Đấu:</strong> @Model.Match.Tournament?.Name</p>
                            <p><strong><PERSON><PERSON><PERSON><PERSON>:</strong> @Model.Match.MatchDate.ToString("dd/MM/yyyy")</p>
                            <p><strong>Th<PERSON><PERSON> Gian:</strong> @(Model.Match.MatchTime?.ToString(@"hh\:mm") ?? "15:00")</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Đội A:</strong> @Model.Match.TeamA</p>
                            <p><strong>Đội B:</strong> @Model.Match.TeamB</p>
                            <p><strong>Tỷ Số:</strong> @(Model.Match.ScoreTeamA ?? 0) - @(Model.Match.ScoreTeamB ?? 0)</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if (TempData["SuccessMessage"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["SuccessMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (TempData["ErrorMessage"] != null)
    {
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            @TempData["ErrorMessage"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Thêm Điểm Số Mới</h5>
                </div>
                <div class="card-body">
                    <form asp-action="AddPlayerScoring" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                        <input type="hidden" asp-for="MatchId" />

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label asp-for="SelectedPlayerId" class="form-label fw-bold">Cầu Thủ:</label>
                                <select asp-for="SelectedPlayerId" class="form-select">
                                    <option value="">-- Chọn Cầu Thủ --</option>
                                    <optgroup label="@Model.Match.TeamA">
                                        @foreach (var player in Model.TeamAPlayers)
                                        {
                                            <option value="@player.PlayerId">@player.FullName (#@player.Number) - @player.Position</option>
                                        }
                                    </optgroup>
                                    <optgroup label="@Model.Match.TeamB">
                                        @foreach (var player in Model.TeamBPlayers)
                                        {
                                            <option value="@player.PlayerId">@player.FullName (#@player.Number) - @player.Position</option>
                                        }
                                    </optgroup>
                                </select>
                                <span asp-validation-for="SelectedPlayerId" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label asp-for="Points" class="form-label fw-bold">Điểm Số:</label>
                                <input asp-for="Points" class="form-control" min="0" max="100" />
                                <span asp-validation-for="Points" class="text-danger"></span>
                            </div>
                            <div class="col-md-3 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-success w-100">
                                    <i class="bi bi-plus-circle"></i> Thêm Điểm Số
                                </button>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label asp-for="Notes" class="form-label fw-bold">Ghi Chú:</label>
                                <textarea asp-for="Notes" class="form-control" rows="2" placeholder="Nhập ghi chú (không bắt buộc)..."></textarea>
                                <span asp-validation-for="Notes" class="text-danger"></span>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">Danh Sách Điểm Số</h5>
                </div>
                <div class="card-body">
                    @if (Model.PlayerScorings.Any())
                    {
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-success">
                                    <tr class="text-center">
                                        <th>Cầu Thủ</th>
                                        <th>Đội</th>
                                        <th>Điểm Số</th>
                                        <th>Thời Gian</th>
                                        <th>Ghi Chú</th>
                                        <th>Hành Động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var scoring in Model.PlayerScorings)
                                    {
                                        <tr class="text-center">
                                            <td>@scoring.Player.FullName (#@scoring.Player.Number)</td>
                                            <td>
                                                <span class="badge @(scoring.Player.Team.Name == Model.Match.TeamA ? "bg-primary" : "bg-danger")">
                                                    @scoring.Player.Team.Name
                                                </span>
                                            </td>
                                            <td class="fw-bold">@scoring.Points</td>
                                            <td>@scoring.ScoringTime.ToString("dd/MM/yyyy HH:mm:ss")</td>
                                            <td>@(string.IsNullOrEmpty(scoring.Notes) ? "-" : scoring.Notes)</td>
                                            <td>
                                                <form asp-action="DeletePlayerScoring" method="post" onsubmit="return confirm('Bạn có chắc chắn muốn xóa điểm số này?');">
                                                    <input type="hidden" name="id" value="@scoring.Id" />
                                                    <input type="hidden" name="matchId" value="@Model.MatchId" />
                                                    <button type="submit" class="btn btn-sm btn-danger">
                                                        <i class="bi bi-trash"></i> Xóa
                                                    </button>
                                                </form>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i> Chưa có điểm số nào được ghi nhận cho trận đấu này.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>

    <div class="d-flex justify-content-between mt-4">
        <a asp-action="Details" asp-route-id="@Model.MatchId" class="btn btn-info">
            <i class="bi bi-arrow-left"></i> Quay Lại Chi Tiết Trận Đấu
        </a>
        <a asp-action="Index" class="btn btn-outline-secondary">
            <i class="bi bi-list"></i> Danh Sách Trận Đấu
        </a>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
