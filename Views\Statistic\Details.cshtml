@model WebQuanLyGiaiDau_NhomTD.Models.Statistic

@{
    ViewData["Title"] = "Chi Tiết Thống Kê";
}

<h1 class="text-center text-info mt-4">📋 <PERSON> Tiết Thống Kê</h1>

<div class="container bg-light shadow rounded p-4 mt-4 mb-4">
    <h5 class="text-primary mb-3">Thông Tin Thống Kê:</h5>
    <dl class="row">
        <dt class="col-sm-3 fw-bold">Cầu Thủ:</dt>
        <dd class="col-sm-9">
            <a asp-controller="Tournament" asp-action="FindPlayerByName" asp-route-playerName="@Model.PlayerName" asp-route-tournamentId="@Model.Match.TournamentId" class="text-decoration-none">
                @Html.DisplayFor(model => model.PlayerName)
                <small class="text-muted"><i class="bi bi-info-circle"></i> Nhấn để xem chi tiết cầu thủ</small>
            </a>
        </dd>

        <dt class="col-sm-3 fw-bold">Đ<PERSON><PERSON>m:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.Points)</dd>

        <dt class="col-sm-3 fw-bold">Kiến Tạo:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.Assists)</dd>

        <dt class="col-sm-3 fw-bold">Bắt Bóng:</dt>
        <dd class="col-sm-9">@Html.DisplayFor(model => model.Rebounds)</dd>

        <dt class="col-sm-3 fw-bold">Trận Đấu:</dt>
        <dd class="col-sm-9">
            <a asp-controller="Match" asp-action="Details" asp-route-id="@Model.MatchId" class="text-decoration-none">
                @Html.DisplayFor(model => model.Match.TeamA) vs @Html.DisplayFor(model => model.Match.TeamB)
                <small class="text-muted"><i class="bi bi-info-circle"></i> Nhấn để xem chi tiết trận đấu</small>
            </a>
        </dd>
    </dl>

    <div class="mt-4 d-flex justify-content-between">
        @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
        {
            <a asp-action="Edit" asp-route-id="@Model?.Id" class="btn btn-warning">
                ✏️ Chỉnh Sửa
            </a>
        }
        else
        {
            <div></div> <!-- Empty div for flex spacing -->
        }
        <a asp-action="Index" class="btn btn-outline-secondary">
            ↩️ Quay Lại Danh Sách
        </a>
    </div>
</div>
