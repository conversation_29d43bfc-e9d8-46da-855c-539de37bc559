﻿@model WebQuanLyGiaiDau_NhomTD.Models.Player

@{
    ViewData["Title"] = "Thêm Cầu Thủ";
}

<h1 class="text-center text-primary mt-4">⚽ Thêm Cầu Thủ Mới</h1>
<hr class="mb-4" />

<div class="row justify-content-center">
    <div class="col-md-6">
        <form asp-action="Create" enctype="multipart/form-data" class="bg-light p-4 rounded shadow">
            <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

            <div class="mb-3">
                <label asp-for="FullName" class="form-label fw-semibold">Họ Tên</label>
                <input asp-for="FullName" class="form-control" placeholder="Nhập họ tên cầu thủ..." />
                <span asp-validation-for="FullName" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Position" class="form-label fw-semibold">Vị Trí</label>
                <input asp-for="Position" class="form-control" placeholder="Vị trí thi đấu..." />
                <span asp-validation-for="Position" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Number" class="form-label fw-semibold">Số Áo</label>
                <input asp-for="Number" class="form-control" placeholder="Số áo..." />
                <span asp-validation-for="Number" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="TeamId" class="form-label fw-semibold">Đội Bóng</label>
                <select asp-for="TeamId" class="form-select" asp-items="ViewBag.TeamId"></select>
            </div>

            <div class="mb-3">
                <label class="form-label fw-semibold">Ảnh Cầu Thủ</label>
                <input type="file" name="imageFile" class="form-control" accept="image/*" />
                <small class="text-muted">Chọn ảnh đại diện cho cầu thủ (không bắt buộc)</small>
            </div>

            <div class="d-grid mt-4">
                <input type="submit" value="Thêm Cầu Thủ" class="btn btn-primary fw-bold" />
            </div>
        </form>

        <div class="text-center mt-3">
            <a asp-action="Index" class="btn btn-outline-secondary">🔙 Quay Lại Danh Sách</a>
        </div>
    </div>
</div>

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
}
