﻿@model WebQuanLyGiaiDau_NhomTD.Models.Team

@{
    ViewData["Title"] = "Thêm Đội Bóng";
}

<h1 class="text-center text-primary mt-4">🏀 Thêm Đội Bóng Mới</h1>
<hr class="mb-4" />

<div class="row justify-content-center">
    <div class="col-md-6">
        <form asp-action="Create" enctype="multipart/form-data" class="bg-light p-4 rounded shadow">
            <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>

            <div class="mb-3">
                <label asp-for="Name" class="form-label fw-semibold">Tên <PERSON></label>
                <input asp-for="Name" class="form-control" placeholder="Nhập tên đội..." />
                <span asp-validation-for="Name" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label asp-for="Coach" class="form-label fw-semibold"><PERSON><PERSON><PERSON> Viên</label>
                <input asp-for="Coach" class="form-control" placeholder="Tên HLV..." />
                <span asp-validation-for="Coach" class="text-danger"></span>
            </div>

            <div class="mb-3">
                <label class="form-label fw-semibold">Logo Đội Bóng</label>
                <input type="file" name="logoFile" class="form-control" accept="image/*" />
                <small class="text-muted">Chọn logo cho đội bóng (không bắt buộc)</small>
            </div>

            <div class="d-grid mt-4">
                <input type="submit" value="Thêm Đội" class="btn btn-success fw-bold" />
            </div>
        </form>

        <div class="text-center mt-3">
            <a asp-action="Index" class="btn btn-outline-secondary">🔙 Quay Lại Danh Sách</a>
        </div>
    </div>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
