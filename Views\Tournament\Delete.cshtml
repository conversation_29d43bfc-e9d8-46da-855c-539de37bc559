@model WebQuanLyGiaiDau_NhomTD.Models.Tournament
@using WebQuanLyGiaiDau_NhomTD.Models.UserModel

@{
    ViewData["Title"] = "Xóa Giải Đấu";
}

<div class="container mt-4">
    <h1 class="text-center text-danger mb-3">
        <i class="bi bi-trash"></i> Xóa Giả<PERSON>
    </h1>
    <h4 class="text-center text-secondary mb-4">Bạn có chắc chắn muốn xóa giải đấu này không?</h4>

@if (ViewData["error"] != null)
{
    <div class="alert alert-danger">
        @ViewData["error"]
    </div>
}

<div class="container bg-light rounded shadow p-4 mb-4">
    <h5 class="text-primary">Thông Tin Giải Đấu:</h5>
    <hr />
    <div class="row">
        <div class="col-md-4">
            @if (!string.IsNullOrEmpty(Model.ImageUrl))
            {
                <img src="@Model.ImageUrl" class="img-fluid rounded mb-3" alt="@Model.Name">
            }
            else
            {
                <div class="bg-light p-4 text-center rounded mb-3">
                    <span class="text-muted fst-italic">Không có hình ảnh</span>
                </div>
            }
        </div>
        <div class="col-md-8">
            <dl class="row">
                <dt class="col-sm-4 fw-bold">Tên Giải Đấu:</dt>
                <dd class="col-sm-8">@Html.DisplayFor(model => model.Name)</dd>

                <dt class="col-sm-4 fw-bold">Mô Tả:</dt>
                <dd class="col-sm-8">@Html.DisplayFor(model => model.Description)</dd>

                <dt class="col-sm-4 fw-bold">Ngày Bắt Đầu:</dt>
                <dd class="col-sm-8">@Model.StartDate.ToString("dd/MM/yyyy")</dd>

                <dt class="col-sm-4 fw-bold">Ngày Kết Thúc:</dt>
                <dd class="col-sm-8">@Model.EndDate.ToString("dd/MM/yyyy")</dd>
            </dl>
        </div>
    </div>

    <form asp-action="Delete">
        <input type="hidden" asp-for="Id" />
        <div class="d-flex justify-content-between mt-4">
            <div>
                <input type="submit" value="🗑️ Xóa" class="btn btn-danger" />
            </div>
            <div>
                @if (Model.SportsId > 0)
                {
                    <a asp-controller="Sports" asp-action="List" asp-route-sportsId="@Model.SportsId" class="btn btn-info me-2">
                        <i class="bi bi-arrow-left"></i> Quay Lại Môn Thể Thao
                    </a>
                }
                <a asp-action="Index" class="btn btn-outline-secondary">↩️ Quay Lại Danh Sách</a>
            </div>
        </div>
    </form>
</div>
</div>
