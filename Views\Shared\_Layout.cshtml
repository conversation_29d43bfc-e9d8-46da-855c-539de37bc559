<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - <PERSON><PERSON><PERSON> 24/7 | Nền Tảng G<PERSON>ải Đấu <PERSON></title>
    <meta name="description" content="Nền tảng quản lý giải đấu thể thao 24/7 hàng đầu Việt Nam. Tham gia ngay để trải nghiệm thể thao đỉnh cao!" />
    <meta name="keywords" content="thể thao, giải đấu, bóng đá, bóng rổ, tournament, sports, 24/7" />

    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&family=Roboto:wght@300;400;500;700;900&family=Orbitron:wght@400;700;900&display=swap">
    <link rel="stylesheet" href="~/css/sports-theme.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/css/rules.css" asp-append-version="true" />
    <link rel="stylesheet" href="~/WebQuanLyGiaiDau_NhomTD.styles.css" asp-append-version="true" />

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="~/favicon.ico">
</head>
<body class="sports-body">
    <!-- Enhanced Page Loader -->
    <div class="page-loader sports-24-7">
        <div class="loading-spinner energy-button"></div>
        <div class="loading-text">🚀 Đang tải Thể Thao 24/7...</div>
    </div>

    <!-- Loading Overlay for AJAX -->
    <div class="loading-overlay">
        <div class="loading-spinner energy-button"></div>
    </div>

    <header>
        <nav class="navbar navbar-expand-lg sports-nav-enhanced sticky-top" id="mainNavbar">
            <div class="container-fluid px-3">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">
                    <i class="bi bi-lightning-charge-fill"></i>
                    <span class="brand-text">Thể Thao 24/7</span>
                    <div class="live-indicator-mini">
                        <div class="pulse-dot"></div>
                        <span>LIVE</span>
                    </div>
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target=".navbar-collapse"
                        aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="navbar-collapse collapse d-lg-flex justify-content-between">
                    <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                        <!-- Trang chủ luôn hiển thị cho tất cả người dùng -->
                        <li class="nav-item nav-item-enhanced">
                            <a class="nav-link" asp-controller="Home" asp-action="Index">
                                <i class="bi bi-house-door-fill"></i>Trang chủ
                            </a>
                        </li>

                        <!-- Tin tức thể thao luôn hiển thị cho tất cả người dùng -->
                        <li class="nav-item nav-item-enhanced">
                            <a class="nav-link" asp-controller="News" asp-action="Index">
                                <i class="bi bi-newspaper"></i>Tin tức
                            </a>
                        </li>

                        <!-- Luật thi đấu luôn hiển thị cho tất cả người dùng -->
                        <li class="nav-item nav-item-enhanced">
                            <a class="nav-link" asp-controller="Rules" asp-action="Index">
                                <i class="bi bi-book-fill"></i>Luật thi đấu
                            </a>
                        </li>

                        @if (User.Identity != null && User.Identity.IsAuthenticated)
                        {
                            <!-- Môn thể thao - hiển thị sau khi đăng nhập -->
                            <li class="nav-item nav-item-enhanced">
                                <a class="nav-link" asp-controller="Sports" asp-action="Index">
                                    <i class="bi bi-dribbble"></i>Môn thể thao
                                </a>
                            </li>

                            <!-- Giải đấu - hiển thị sau khi đăng nhập -->
                            <li class="nav-item nav-item-enhanced">
                                <a class="nav-link" asp-controller="Tournament" asp-action="Index">
                                    <i class="bi bi-trophy-fill"></i>Giải đấu
                                </a>
                            </li>

                            @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                            {
                                <!-- Admin-specific navigation -->
                                <li class="nav-item nav-item-enhanced dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" data-bs-toggle="dropdown">
                                        <i class="bi bi-gear-fill"></i>Quản lý
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                                        <li>
                                            <a class="dropdown-item" asp-controller="Teams" asp-action="Index">
                                                <i class="bi bi-people-fill"></i> Đội bóng
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Players" asp-action="Index">
                                                <i class="bi bi-person-fill"></i> Cầu thủ
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Match" asp-action="Index">
                                                <i class="bi bi-calendar-event"></i> Trận đấu
                                            </a>
                                        </li>

                                        <li>
                                            <a class="dropdown-item" asp-controller="News" asp-action="Index">
                                                <i class="bi bi-newspaper"></i> Quản lý tin tức
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="ManageRegistrations">
                                                <i class="bi bi-person-check"></i> Đăng ký người dùng
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="ManageTeamRegistrations">
                                                <i class="bi bi-people-check"></i> Đăng ký đội
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            }

                            @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_User))
                            {
                                <!-- User-specific navigation -->
                                <li class="nav-item nav-item-enhanced dropdown">
                                    <a class="nav-link dropdown-toggle" href="#" id="userDropdown" data-bs-toggle="dropdown">
                                        <i class="bi bi-person-gear"></i>Quản lý
                                    </a>
                                    <ul class="dropdown-menu" aria-labelledby="userDropdown">
                                        <li>
                                            <a class="dropdown-item" asp-controller="Teams" asp-action="Index">
                                                <i class="bi bi-people-fill"></i> Đội bóng
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="Index">
                                                <i class="bi bi-trophy"></i> Giải đấu
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Tournament" asp-action="MyRegistrations">
                                                <i class="bi bi-list-check"></i> Đăng ký giải đấu
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" asp-controller="Home" asp-action="UserGuide">
                                                <i class="bi bi-question-circle"></i> Hướng dẫn sử dụng
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                            }
                        }
                        else
                        {
                            <!-- Navigation for non-authenticated users -->
                            <li class="nav-item">
                                <a class="nav-link" asp-controller="Home" asp-action="Privacy">
                                    <i class="bi bi-shield-check"></i> Chính sách
                                </a>
                            </li>
                        }
                    </ul>

                    <div class="d-flex">
                        <partial name="_LoginPartial" />
                    </div>
                </div>
            </div>
        </nav>
    </header>

    <main role="main" class="main-content">
        @RenderBody()
    </main>

    <footer class="sports-footer sports-24-7">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 col-md-6 mb-4">
                    <h5>⚡ Thể Thao 24/7</h5>
                    <p class="mb-4">🚀 Nền tảng quản lý giải đấu thể thao năng động nhất Việt Nam! Trải nghiệm thể thao đỉnh cao 24/7 cùng chúng tôi!</p>
                    <div class="social-links">
                        <a href="#" class="me-3" data-bs-toggle="tooltip" title="Facebook">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="me-3" data-bs-toggle="tooltip" title="Twitter">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="me-3" data-bs-toggle="tooltip" title="Instagram">
                            <i class="bi bi-instagram"></i>
                        </a>
                        <a href="#" data-bs-toggle="tooltip" title="YouTube">
                            <i class="bi bi-youtube"></i>
                        </a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h5>🔗 Liên kết HOT</h5>
                    <ul class="list-unstyled">
                        <li><a asp-controller="Home" asp-action="Index" class="energy-button"><i class="bi bi-house-door-fill"></i>🏠 Trang chủ</a></li>
                        <li><a asp-controller="News" asp-action="Index" class="energy-button"><i class="bi bi-newspaper"></i>📰 Tin tức HOT</a></li>
                        <li><a asp-controller="Rules" asp-action="Index" class="energy-button"><i class="bi bi-book-fill"></i>📖 Luật thi đấu</a></li>
                        <li><a asp-controller="Home" asp-action="Privacy" class="energy-button"><i class="bi bi-shield-check-fill"></i>🛡️ Chính sách</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>🚀 Dịch vụ VIP</h5>
                    <ul class="list-unstyled">
                        <li><a href="#" class="energy-button"><i class="bi bi-trophy-fill"></i>🏆 Giải đấu LIVE</a></li>
                        <li><a href="#" class="energy-button"><i class="bi bi-people-fill"></i>⚽ Đội hình siêu sao</a></li>
                        <li><a href="#" class="energy-button"><i class="bi bi-calendar-event-fill"></i>📅 Lịch thi đấu AI</a></li>
                        <li><a href="#" class="energy-button"><i class="bi bi-bar-chart-fill"></i>📊 Analytics Pro</a></li>
                    </ul>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <h5>📞 Liên hệ 24/7</h5>
                    <ul class="list-unstyled">
                        <li><a href="mailto:<EMAIL>" class="energy-button"><i class="bi bi-envelope-fill"></i>📧 <EMAIL></a></li>
                        <li><a href="tel:+84123456789" class="energy-button"><i class="bi bi-telephone-fill"></i>📱 (+84) 123 456 789</a></li>
                        <li><a href="#" class="energy-button"><i class="bi bi-geo-alt-fill"></i>📍 Hà Nội, Việt Nam</a></li>
                        <li><a href="#" class="energy-button"><i class="bi bi-clock-fill"></i>⏰ 24/7 Hỗ trợ LIVE</a></li>
                    </ul>
                </div>
            </div>
            <hr class="footer-divider">
            <div class="footer-bottom">
                <p>&copy; 2025 ⚡ Thể Thao 24/7 - Nền tảng giải đấu năng động #1 Việt Nam. 🚀 Mọi quyền được bảo lưu.</p>
            </div>
        </div>
    </footer>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/sports-animations.js" asp-append-version="true"></script>
    @await RenderSectionAsync("Scripts", required: false)

</body>
</html>
