@model WebQuanLyGiaiDau_NhomTD.Models.Statistic

@{
    ViewData["Title"] = "Chỉnh Sửa T<PERSON>ống <PERSON>ê";
}

<h1 class="text-center text-warning mt-4">✏️ Chỉnh <PERSON><PERSON><PERSON></h1>

<div class="container bg-light rounded shadow p-4 mt-4 mb-4">
    <form asp-action="Edit">
        <div asp-validation-summary="ModelOnly" class="text-danger mb-3"></div>
        <input type="hidden" asp-for="Id" />

        <div class="mb-3">
            <label asp-for="PlayerName" class="form-label fw-bold">Tê<PERSON>ủ:</label>
            <input asp-for="PlayerName" class="form-control" />
            <span asp-validation-for="PlayerName" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Points" class="form-label fw-bold">Đ<PERSON>ểm:</label>
            <input asp-for="Points" class="form-control" type="number" min="0" />
            <span asp-validation-for="Points" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Assists" class="form-label fw-bold">Kiến Tạo:</label>
            <input asp-for="Assists" class="form-control" type="number" min="0" />
            <span asp-validation-for="Assists" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="Rebounds" class="form-label fw-bold">Bắt Bóng:</label>
            <input asp-for="Rebounds" class="form-control" type="number" min="0" />
            <span asp-validation-for="Rebounds" class="text-danger"></span>
        </div>

        <div class="mb-3">
            <label asp-for="MatchId" class="form-label fw-bold">Trận Đấu:</label>
            <select asp-for="MatchId" class="form-select" asp-items="ViewBag.MatchId"></select>
            <span asp-validation-for="MatchId" class="text-danger"></span>
        </div>

        <div class="d-flex justify-content-between mt-4">
            <input type="submit" value="💾 Lưu Thay Đổi" class="btn btn-success" />
            <a asp-action="Index" class="btn btn-outline-secondary">↩️ Quay Lại Danh Sách</a>
        </div>
    </form>
</div>

@section Scripts {
    @{
        await Html.RenderPartialAsync("_ValidationScriptsPartial");
    }
}
