@model IEnumerable<WebQuanLyGiaiDau_NhomTD.Models.TournamentSubmission>

@{
    ViewData["Title"] = "Quản lý bài viết/tài liệu";
}

<div class="container mt-4">
    <h1 class="text-center text-primary mb-4">Quản lý bài viết/tài liệu</h1>

    <div class="mb-3">
        <a asp-action="Index" class="btn btn-secondary">
            <i class="bi bi-arrow-left"></i> Quay lại danh sách giải đấu
        </a>

    </div>

    @if (TempData["Message"] != null)
    {
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            @TempData["Message"]
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    }

    @if (!Model.Any())
    {
        <div class="alert alert-info">
            <h4 class="alert-heading">Chưa có bài nộp!</h4>
            <p>Hiện tại chưa có người dùng nào nộp bài viết hoặc tài liệu.</p>
        </div>
    }
    else
    {
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h3 class="card-title mb-0">Danh sách bài viết/tài liệu</h3>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>ID</th>
                                <th>Người dùng</th>
                                <th>Giải đấu</th>
                                <th>Tiêu đề</th>
                                <th>Ngày nộp</th>
                                <th>Tài liệu</th>
                                <th>Trạng thái</th>
                                <th>Thao tác</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var submission in Model)
                            {
                                <tr>
                                    <td>@submission.Id</td>
                                    <td>@submission.User.FullName</td>
                                    <td>@submission.Tournament.Name</td>
                                    <td>@submission.Title</td>
                                    <td>@submission.SubmissionDate.ToString("dd/MM/yyyy HH:mm")</td>
                                    <td>
                                        @if (!string.IsNullOrEmpty(submission.FileUrl))
                                        {
                                            <a href="@submission.FileUrl" target="_blank" class="btn btn-sm btn-outline-primary">
                                                <i class="bi bi-file-earmark"></i> @submission.FileName
                                            </a>
                                        }
                                        else
                                        {
                                            <span class="text-muted fst-italic">Không có</span>
                                        }
                                    </td>
                                    <td>
                                        @switch (submission.Status)
                                        {
                                            case "Pending":
                                                <span class="badge bg-warning text-dark">Đang chờ duyệt</span>
                                                break;
                                            case "Approved":
                                                <span class="badge bg-success">Đã duyệt</span>
                                                break;
                                            case "Rejected":
                                                <span class="badge bg-danger">Từ chối</span>
                                                break;
                                            default:
                                                <span class="badge bg-secondary">@submission.Status</span>
                                                break;
                                        }
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                                <i class="bi bi-eye"></i> Xem
                                            </button>
                                            <a asp-action="EditSubmission" asp-route-id="@submission.Id" class="btn btn-sm btn-warning">
                                                <i class="bi bi-pencil"></i> Sửa
                                            </a>
                                            <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                                <i class="bi bi-trash"></i> Xóa
                                            </button>
                                        </div>

                                        @if (submission.Status == "Pending")
                                        {
                                            <div class="mt-2">
                                                <button type="button" class="btn btn-sm btn-success" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                                    <i class="bi bi-check-circle"></i> Duyệt
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#<EMAIL>">
                                                    <i class="bi bi-x-circle"></i> Từ chối
                                                </button>
                                            </div>
                                        }
                                    </td>
                                </tr>

                                <!-- Modal Xem chi tiết -->
                                <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>" aria-hidden="true">
                                    <div class="modal-dialog modal-lg">
                                        <div class="modal-content">
                                            <div class="modal-header bg-primary text-white">
                                                <h5 class="modal-title" id="<EMAIL>">Chi tiết bài viết</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                            </div>
                                            <div class="modal-body">
                                                <h4>@submission.Title</h4>
                                                <p class="text-muted">
                                                    <strong>Người nộp:</strong> @submission.User.FullName |
                                                    <strong>Giải đấu:</strong> @submission.Tournament.Name |
                                                    <strong>Ngày nộp:</strong> @submission.SubmissionDate.ToString("dd/MM/yyyy HH:mm")
                                                </p>

                                                <div class="card mb-3">
                                                    <div class="card-header">Nội dung</div>
                                                    <div class="card-body">
                                                        <p>@submission.Content</p>
                                                    </div>
                                                </div>

                                                @if (!string.IsNullOrEmpty(submission.FileUrl))
                                                {
                                                    <div class="mb-3">
                                                        <strong>Tài liệu đính kèm:</strong>
                                                        <a href="@submission.FileUrl" target="_blank" class="btn btn-sm btn-outline-primary">
                                                            <i class="bi bi-file-earmark"></i> @submission.FileName
                                                        </a>
                                                    </div>
                                                }

                                                <div class="mb-3">
                                                    <strong>Trạng thái:</strong>
                                                    @switch (submission.Status)
                                                    {
                                                        case "Pending":
                                                            <span class="badge bg-warning text-dark">Đang chờ duyệt</span>
                                                            break;
                                                        case "Approved":
                                                            <span class="badge bg-success">Đã duyệt</span>
                                                            break;
                                                        case "Rejected":
                                                            <span class="badge bg-danger">Từ chối</span>
                                                            break;
                                                        default:
                                                            <span class="badge bg-secondary">@submission.Status</span>
                                                            break;
                                                    }
                                                </div>

                                                @if (!string.IsNullOrEmpty(submission.AdminNotes))
                                                {
                                                    <div class="card">
                                                        <div class="card-header">Ghi chú của admin</div>
                                                        <div class="card-body">
                                                            <p>@submission.AdminNotes</p>
                                                        </div>
                                                    </div>
                                                }
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Đóng</button>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal Duyệt -->
                                <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form asp-action="ApproveSubmission" method="post">
                                                <div class="modal-header bg-success text-white">
                                                    <h5 class="modal-title" id="<EMAIL>">Duyệt bài viết</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <input type="hidden" name="id" value="@submission.Id">
                                                    <p>Bạn có chắc chắn muốn duyệt bài viết "<strong>@submission.Title</strong>" không?</p>
                                                    <div class="mb-3">
                                                        <label for="<EMAIL>" class="form-label">Ghi chú (tùy chọn)</label>
                                                        <textarea id="<EMAIL>" name="adminNotes" class="form-control" rows="3"></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                                    <button type="submit" class="btn btn-success">Duyệt</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal Từ chối -->
                                <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form asp-action="RejectSubmission" method="post">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title" id="<EMAIL>">Từ chối bài viết</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <input type="hidden" name="id" value="@submission.Id">
                                                    <p>Bạn có chắc chắn muốn từ chối bài viết "<strong>@submission.Title</strong>" không?</p>
                                                    <div class="mb-3">
                                                        <label for="<EMAIL>" class="form-label">Lý do từ chối</label>
                                                        <textarea id="<EMAIL>" name="adminNotes" class="form-control" rows="3" required></textarea>
                                                    </div>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                                    <button type="submit" class="btn btn-danger">Từ chối</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>

                                <!-- Modal Xóa -->
                                <div class="modal fade" id="<EMAIL>" tabindex="-1" aria-labelledby="<EMAIL>" aria-hidden="true">
                                    <div class="modal-dialog">
                                        <div class="modal-content">
                                            <form asp-action="DeleteSubmission" method="post">
                                                <div class="modal-header bg-danger text-white">
                                                    <h5 class="modal-title" id="<EMAIL>">Xóa bài viết</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    <input type="hidden" name="id" value="@submission.Id">
                                                    <p>Bạn có chắc chắn muốn xóa bài viết "<strong>@submission.Title</strong>" không?</p>
                                                    <p class="text-danger">Lưu ý: Hành động này không thể hoàn tác!</p>
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Hủy</button>
                                                    <button type="submit" class="btn btn-danger">Xóa</button>
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    }
</div>
