@model WebQuanLyGiaiDau_NhomTD.Models.News

@{
    ViewData["Title"] = Model.Title;
}

<div class="container py-5">
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a asp-controller="Home" asp-action="Index">Trang chủ</a></li>
            <li class="breadcrumb-item"><a asp-controller="News" asp-action="Index">Tin tức</a></li>
            <li class="breadcrumb-item active" aria-current="page">@Model.Title</li>
        </ol>
    </nav>

    <div class="row">
        <div class="col-lg-8">
            <article class="news-article">
                <h1 class="news-title mb-3">@Model.Title</h1>
                
                <div class="news-meta mb-4">
                    <span class="me-3"><i class="bi bi-calendar-event me-1"></i>@Model.PublishDate.ToString("dd/MM/yyyy HH:mm")</span>
                    <span class="me-3"><i class="bi bi-person me-1"></i>@Model.Author</span>
                    <span class="me-3"><i class="bi bi-eye me-1"></i>@Model.ViewCount lượt xem</span>
                    <span><i class="bi bi-tag me-1"></i>@Model.Category</span>
                </div>
                
                @if (!string.IsNullOrEmpty(Model.ImageUrl))
                {
                    <div class="news-featured-image mb-4">
                        <img src="@Model.ImageUrl" alt="@Model.Title" class="img-fluid rounded">
                    </div>
                }
                
                <div class="news-summary mb-4">
                    <p class="lead">@Model.Summary</p>
                </div>
                
                <div class="news-content">
                    @Html.Raw(Model.Content)
                </div>
                
                <div class="news-tags mt-4">
                    @if (!string.IsNullOrEmpty(Model.Category))
                    {
                        <span class="badge bg-primary me-2">@Model.Category</span>
                    }
                    @if (Model.Sports != null)
                    {
                        <span class="badge bg-secondary me-2">@Model.Sports.Name</span>
                    }
                </div>
                
                <div class="news-actions mt-5">
                    <div class="d-flex justify-content-between">
                        <a asp-action="Index" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Quay lại danh sách tin tức
                        </a>
                        
                        @if (User.IsInRole(WebQuanLyGiaiDau_NhomTD.Models.UserModel.SD.Role_Admin))
                        {
                            <div>
                                <a asp-action="Edit" asp-route-id="@Model.NewsId" class="btn btn-warning me-2">
                                    <i class="bi bi-pencil me-1"></i>Sửa
                                </a>
                                <a asp-action="Delete" asp-route-id="@Model.NewsId" class="btn btn-danger">
                                    <i class="bi bi-trash me-1"></i>Xóa
                                </a>
                            </div>
                        }
                    </div>
                </div>
            </article>
        </div>
        
        <div class="col-lg-4">
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="bi bi-share me-2"></i>Chia sẻ</h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-around">
                        <a href="#" class="btn btn-outline-primary" onclick="window.open('https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(window.location.href), '_blank')">
                            <i class="bi bi-facebook"></i>
                        </a>
                        <a href="#" class="btn btn-outline-info" onclick="window.open('https://twitter.com/intent/tweet?url=' + encodeURIComponent(window.location.href) + '&text=' + encodeURIComponent('@Model.Title'), '_blank')">
                            <i class="bi bi-twitter"></i>
                        </a>
                        <a href="#" class="btn btn-outline-success" onclick="window.open('https://api.whatsapp.com/send?text=' + encodeURIComponent('@Model.Title') + '%20' + encodeURIComponent(window.location.href), '_blank')">
                            <i class="bi bi-whatsapp"></i>
                        </a>
                        <a href="#" class="btn btn-outline-secondary" onclick="navigator.clipboard.writeText(window.location.href); alert('Đã sao chép liên kết!')">
                            <i class="bi bi-link-45deg"></i>
                        </a>
                    </div>
                </div>
            </div>
            
            @if (Model.Sports != null)
            {
                <div class="card mb-4">
                    <div class="card-header bg-secondary text-white">
                        <h5 class="mb-0"><i class="bi bi-dribbble me-2"></i>Thông tin môn thể thao</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            @if (!string.IsNullOrEmpty(Model.Sports.ImageUrl))
                            {
                                <img src="@Model.Sports.ImageUrl" alt="@Model.Sports.Name" class="img-fluid" style="max-height: 100px;">
                            }
                            else
                            {
                                <i class="bi bi-dribbble display-4"></i>
                            }
                        </div>
                        <h5 class="text-center">@Model.Sports.Name</h5>
                        <div class="d-grid gap-2 mt-3">
                            <a asp-controller="Tournament" asp-action="Index" asp-route-sportsId="@Model.Sports.Id" class="btn btn-outline-secondary">
                                <i class="bi bi-trophy me-2"></i>Xem giải đấu
                            </a>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

<style>
    .news-article {
        font-size: 1.1rem;
        line-height: 1.7;
    }
    
    .news-title {
        font-size: 2.5rem;
        font-weight: 700;
    }
    
    .news-meta {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .news-featured-image {
        border-radius: 10px;
        overflow: hidden;
    }
    
    .news-featured-image img {
        width: 100%;
        max-height: 500px;
        object-fit: cover;
    }
    
    .news-summary {
        font-size: 1.2rem;
        color: #495057;
        border-left: 4px solid var(--primary-color, #0066cc);
        padding-left: 1rem;
    }
    
    .news-content {
        margin-bottom: 2rem;
    }
    
    .news-content img {
        max-width: 100%;
        height: auto;
        margin: 1rem 0;
        border-radius: 5px;
    }
    
    .news-content p {
        margin-bottom: 1.5rem;
    }
    
    .news-content h2, .news-content h3 {
        margin-top: 2rem;
        margin-bottom: 1rem;
    }
    
    .news-tags .badge {
        font-size: 0.9rem;
        padding: 0.5rem 1rem;
    }
</style>
