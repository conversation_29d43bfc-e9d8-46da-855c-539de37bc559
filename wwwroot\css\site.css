html {
  font-size: 14px;
}

@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.btn:focus, .btn:active:focus, .btn-link.nav-link:focus, .form-control:focus, .form-check-input:focus {
  box-shadow: 0 0 0 0.1rem white, 0 0 0 0.25rem #258cfb;
}

html {
  position: relative;
  min-height: 100%;
}

body {
  margin-bottom: 60px;
}

.form-floating > .form-control-plaintext::placeholder, .form-floating > .form-control::placeholder {
  color: var(--bs-secondary-color);
  text-align: end;
}

.form-floating > .form-control-plaintext:focus::placeholder, .form-floating > .form-control:focus::placeholder {
  text-align: start;
}

.navbar {
    font-size: 1.05rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

.dropdown-menu {
    border-radius: 10px;
    padding: 0.5rem;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
}

.dropdown-item:hover {
    background-color: #e3f2fd;
    border-radius: 8px;
}

.btn-info {
    background-color: #00b0f0;
    border-color: #00b0f0;
}

    .btn-info:hover {
        background-color: #0095d9;
    }

/* User profile dropdown styles */
.navbar-nav .dropdown-menu {
    right: 0;
    left: auto;
    min-width: 200px;
}

.navbar-nav .dropdown-toggle::after {
    vertical-align: middle;
}

.user-profile-dropdown {
    font-weight: 500;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
}

.dropdown-item i {
    width: 20px;
    text-align: center;
    margin-right: 8px;
}

#logout {
    background: none;
    border: none;
    width: 100%;
    text-align: left;
    padding: 0.5rem 1rem;
    color: #dc3545;
}

#logout:hover {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

/* Rules Page Styles */
.rule-nav {
    position: sticky;
    top: 20px;
}

.rule-section {
    scroll-margin-top: 20px;
}

.rule-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
    border-top: 4px solid #0d6efd;
}

.rule-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.rule-card .card-title {
    color: #0d6efd;
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.table-differences th {
    background-color: #0d6efd;
    color: white;
}

.table-differences {
    border-radius: 10px;
    overflow: hidden;
}

/* 3x3 Basketball specific styles */
#category-8 .rule-card {
    border-top-color: #ff6b00;
}

#category-8 .card-header {
    background-color: #ff6b00 !important;
}

#category-8 .rule-card .card-title {
    color: #ff6b00;
}

#category-8 .rule-card:hover {
    box-shadow: 0 10px 20px rgba(255, 107, 0, 0.2);
}

/* FAQ Accordion Styles */
.accordion-button:not(.collapsed) {
    background-color: #e7f1ff;
    color: #0d6efd;
}

.accordion-button:focus {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.accordion-item {
    margin-bottom: 10px;
    border-radius: 8px;
    overflow: hidden;
}

/* Team name responsive styles */
.team-name {
    font-size: 1.5rem;
    font-weight: 600;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .rule-nav {
        position: static;
        margin-bottom: 20px;
    }

    .rule-card {
        margin-bottom: 15px;
    }

    /* Responsive table adjustments */
    .table-responsive {
        font-size: 0.9rem;
    }

    /* Team name responsive styles */
    .team-name {
        font-size: 1.2rem;
    }

    /* Adjust card padding on mobile */
    .card-body {
        padding: 1rem;
    }

    /* Improve button spacing on mobile */
    .btn {
        margin-bottom: 0.5rem;
    }
}

/* Extra small devices */
@media (max-width: 576px) {
    .team-name {
        font-size: 1rem;
    }

    h1 {
        font-size: 1.8rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .container {
        padding-left: 10px;
        padding-right: 10px;
    }
}
